import re
from typing import Dict, Any, <PERSON>, Optional, Tuple
from datetime import datetime

def validate_lab_results(data: Dict[str, Any]) -> List[str]:
    """Validate lab results data"""
    errors = []
    
    # Validate lab_type
    valid_lab_types = ["Basic_Metabolic", "CBC", "Liver_Function", "Thyroid", "Toxicology", "Inflammatory_Markers"]
    if 'lab_type' not in data:
        errors.append("Missing lab_type")
    elif data['lab_type'] not in valid_lab_types:
        errors.append(f"Invalid lab_type: {data['lab_type']}")
    
    # Validate collection_date
    if 'collection_date' in data:
        try:
            # Try to parse as datetime if it's a string
            if isinstance(data['collection_date'], str):
                datetime.fromisoformat(data['collection_date'])
        except (ValueError, TypeError):
            errors.append("collection_date must be a valid ISO datetime string")
    
    # Validate raw_values
    if 'raw_values' not in data:
        errors.append("Missing raw_values")
    elif not isinstance(data['raw_values'], dict):
        errors.append("raw_values must be a dictionary")
    
    # Validate reference_ranges with strengthened checks
    if 'reference_ranges' not in data:
        errors.append("Missing reference_ranges")
    elif not isinstance(data['reference_ranges'], dict):
        errors.append("reference_ranges must be a dictionary")
    else:
        # Check that reference ranges are tuples of exactly two floats
        for param, range_val in data['reference_ranges'].items():
            if not isinstance(range_val, (list, tuple)) or len(range_val) != 2:
                errors.append(f"reference_ranges for {param} must be a tuple/list of exactly 2 values")
            elif not all(isinstance(v, (int, float)) for v in range_val):
                errors.append(f"reference_ranges for {param} must contain numeric values")
            elif range_val[0] >= range_val[1]:
                errors.append(f"reference_ranges for {param} must have min < max")
    
    return errors

def validate_pid_format(pid: str) -> bool:
    """Validate PID format: PID_[16char_data][4char_checksum]"""
    pid_regex = re.compile(r'^PID_[A-Za-z0-9+/]{16}[A-Za-z0-9]{4}$')
    return bool(pid_regex.match(pid))

def validate_symptom_data(symptom_data: Dict[str, Any]) -> List[str]:
    """Validate symptom assessment data"""
    errors = []
    
    valid_domains = ['mood_symptoms', 'psychotic_symptoms', 'anxiety_symptoms', 'cognitive_symptoms', 'behavioral_symptoms']
    valid_severities = ['Mild', 'Moderate', 'Severe']
    valid_impacts = ['None', 'Mild', 'Moderate', 'Severe']
    
    for domain in valid_domains:
        if domain in symptom_data:
            domain_symptoms = symptom_data[domain]
            if not isinstance(domain_symptoms, dict):
                errors.append(f"{domain} must be a dictionary")
                continue
                
            for symptom_name, symptom_item in domain_symptoms.items():
                if not isinstance(symptom_item, dict):
                    errors.append(f"{domain}.{symptom_name} must be a dictionary")
                    continue
                
                # Validate required 'present' field
                if 'present' not in symptom_item:
                    errors.append(f"{domain}.{symptom_name} missing 'present' field")
                elif not isinstance(symptom_item['present'], bool):
                    errors.append(f"{domain}.{symptom_name}.present must be boolean")
                
                # Validate severity if present
                if 'severity' in symptom_item and symptom_item['severity'] is not None:
                    if symptom_item['severity'] not in valid_severities:
                        errors.append(f"{domain}.{symptom_name}.severity must be one of {valid_severities}")
                
                # Validate functional_impact if present
                if 'functional_impact' in symptom_item and symptom_item['functional_impact'] is not None:
                    if symptom_item['functional_impact'] not in valid_impacts:
                        errors.append(f"{domain}.{symptom_name}.functional_impact must be one of {valid_impacts}")
                
                # Validate duration_weeks if present
                if 'duration_weeks' in symptom_item and symptom_item['duration_weeks'] is not None:
                    if not isinstance(symptom_item['duration_weeks'], int) or symptom_item['duration_weeks'] < 0:
                        errors.append(f"{domain}.{symptom_name}.duration_weeks must be a non-negative integer")
    
    return errors
