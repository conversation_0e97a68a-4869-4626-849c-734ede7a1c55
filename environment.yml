name: myenv
channels:
  - conda-forge
  - defaults
dependencies:
  - python=3.11
  - pip
  - nodejs=18
  - npm
  - git
  
  # Core FastAPI and web framework
  - fastapi
  - uvicorn
  - python-multipart
  - aiofiles
  - jinja2
  
  # Data validation and settings
  - pydantic
  - pydantic-settings
  
  # Machine Learning
  - scikit-learn=1.3.2
  - pandas=2.1.4
  - numpy=1.25.2
  - joblib=1.3.2
  
  # Security and Authentication
  - passlib
  - cryptography
  
  # Logging
  - loguru
  
  # Data export formats
  - pyarrow=14.0.1
  
  # HTTP client for testing
  - httpx
  
  # System monitoring
  - psutil
  
  # Development and testing
  - pytest
  - pytest-asyncio
  - pytest-cov
  
  # Environment management
  - python-dotenv
  
  # Additional dependencies via pip
  - pip:
    - surrealdb==0.3.2
    - python-jose[cryptography]==3.3.0
    - argon2-cffi
