from fastapi import APIRouter, HTTPException, Depends, status, Response
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import Dict, List, Any, Optional, Literal
from datetime import datetime
import pandas as pd
import json
import io
import csv
from database.connection import SurrealDBManager
from security.auth_manager import get_current_active_user
from utils.logging import logger
from dependencies import get_db

router = APIRouter(prefix="/exports", tags=["data_export"])

class ExportRequest(BaseModel):
    format: Literal["csv", "json", "parquet"] = "csv"
    include_demographics: bool = True
    include_symptoms: bool = True
    include_history: bool = True
    include_predictions: bool = False
    date_from: Optional[str] = None
    date_to: Optional[str] = None
    anonymize: bool = True

class ExportResponse(BaseModel):
    export_id: str
    status: str
    message: str
    download_url: Optional[str] = None

@router.post("/request", response_model=ExportResponse)
async def request_data_export(
    request: ExportRequest,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    db: SurrealDBManager = Depends(get_db)
):
    """Request data export in specified format"""
    
    # Check permissions - only researchers and admins can export data
    if not any(role in current_user.get("roles", []) for role in ["researcher", "admin"]):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Researcher or admin role required for data export"
        )
    
    try:
        # Build filters
        filters = {}
        
        if "admin" not in current_user.get("roles", []):
            # Non-admin users can only export their own data
            filters["clinician_id"] = current_user["user_id"]
        
        if request.date_from:
            try:
                filters["date_from"] = datetime.fromisoformat(request.date_from)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid date_from format. Use ISO format (YYYY-MM-DD)"
                )
        
        if request.date_to:
            try:
                filters["date_to"] = datetime.fromisoformat(request.date_to)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid date_to format. Use ISO format (YYYY-MM-DD)"
                )
        
        # Get patient data
        patients = await db.search_patients(filters)
        
        if not patients:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No data found for export with the specified criteria"
            )
        
        # Prepare export data
        export_data = await _prepare_export_data(
            patients, 
            request, 
            db if request.include_predictions else None
        )
        
        # Generate export file
        if request.format == "csv":
            file_content = _generate_csv(export_data, request.anonymize)
            media_type = "text/csv"
            filename = f"psychiatric_data_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        elif request.format == "json":
            file_content = _generate_json(export_data, request.anonymize)
            media_type = "application/json"
            filename = f"psychiatric_data_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        elif request.format == "parquet":
            file_content = _generate_parquet(export_data, request.anonymize)
            media_type = "application/octet-stream"
            filename = f"psychiatric_data_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.parquet"
        
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Unsupported export format"
            )
        
        logger.info(
            f"Data export requested by {current_user['username']}: "
            f"{len(patients)} records, format: {request.format}, anonymized: {request.anonymize}"
        )
        
        # Return file as streaming response
        return StreamingResponse(
            io.BytesIO(file_content),
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except Exception as e:
        logger.error(f"Failed to export data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to export data: {str(e)}"
        )

async def _prepare_export_data(
    patients: List[Dict[str, Any]], 
    request: ExportRequest, 
    db: Optional[SurrealDBManager] = None
) -> List[Dict[str, Any]]:
    """Prepare data for export based on request parameters"""
    
    export_records = []
    
    for patient in patients:
        record = {}
        
        # Always include PID (will be anonymized if requested)
        record["pid"] = patient["pid"]
        record["created_at"] = patient["created_at"]
        record["updated_at"] = patient["updated_at"]
        record["completeness_score"] = patient.get("completeness_score", 0)
        
        # Include demographics
        if request.include_demographics and patient.get("demographics"):
            demographics = patient["demographics"]
            record.update({
                f"demo_{key}": value for key, value in demographics.items()
            })
        
        # Include symptoms
        if request.include_symptoms and patient.get("symptoms"):
            symptoms = patient["symptoms"]
            # Flatten symptom data
            for domain, domain_symptoms in symptoms.items():
                if isinstance(domain_symptoms, dict):
                    for symptom_name, symptom_data in domain_symptoms.items():
                        if isinstance(symptom_data, dict):
                            record[f"symptom_{domain}_{symptom_name}_present"] = symptom_data.get("present", False)
                            record[f"symptom_{domain}_{symptom_name}_severity"] = symptom_data.get("severity")
                            record[f"symptom_{domain}_{symptom_name}_duration"] = symptom_data.get("duration_weeks")
        
        # Include clinical history
        if request.include_history and patient.get("clinical_history"):
            history = patient["clinical_history"]
            # Flatten history data
            record["history_previous_diagnoses_count"] = len(history.get("previous_diagnoses", []))
            record["history_hospitalization_count"] = history.get("hospitalization_count", 0)
            record["history_suicide_attempts"] = history.get("suicide_attempts", 0)
            record["history_family_psychiatric_history"] = history.get("family_psychiatric_history", False)
            record["history_trauma_history"] = history.get("trauma_history", False)
            record["history_social_support_level"] = history.get("social_support_level")
            record["history_housing_stability"] = history.get("housing_stability")
            
            # Substance use
            substance_use = history.get("substance_use", {})
            for substance, use_data in substance_use.items():
                if isinstance(use_data, dict):
                    record[f"substance_{substance}_current_use"] = use_data.get("current_use", False)
                    record[f"substance_{substance}_frequency"] = use_data.get("frequency")
                    record[f"substance_{substance}_severity"] = use_data.get("severity")
        
        # Include ML predictions if requested
        if request.include_predictions and db:
            # This would require implementing a method to get predictions for a patient
            # For now, we'll skip this
            pass
        
        export_records.append(record)
    
    return export_records

def _generate_csv(data: List[Dict[str, Any]], anonymize: bool) -> bytes:
    """Generate CSV export"""
    if not data:
        return b""
    
    # Anonymize PIDs if requested
    if anonymize:
        for record in data:
            if "pid" in record:
                record["pid"] = f"ANON_{hash(record['pid']) % 100000:05d}"
    
    # Create CSV
    output = io.StringIO()
    
    # Get all possible field names
    all_fields = set()
    for record in data:
        all_fields.update(record.keys())
    
    fieldnames = sorted(list(all_fields))
    
    writer = csv.DictWriter(output, fieldnames=fieldnames)
    writer.writeheader()
    
    for record in data:
        # Convert None values to empty strings for CSV
        csv_record = {k: (v if v is not None else "") for k, v in record.items()}
        writer.writerow(csv_record)
    
    return output.getvalue().encode('utf-8')

def _generate_json(data: List[Dict[str, Any]], anonymize: bool) -> bytes:
    """Generate JSON export"""
    if anonymize:
        for record in data:
            if "pid" in record:
                record["pid"] = f"ANON_{hash(record['pid']) % 100000:05d}"
    
    # Convert datetime objects to ISO strings
    def json_serializer(obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
    
    json_str = json.dumps(data, indent=2, default=json_serializer)
    return json_str.encode('utf-8')

def _generate_parquet(data: List[Dict[str, Any]], anonymize: bool) -> bytes:
    """Generate Parquet export"""
    if anonymize:
        for record in data:
            if "pid" in record:
                record["pid"] = f"ANON_{hash(record['pid']) % 100000:05d}"
    
    # Convert to DataFrame
    df = pd.DataFrame(data)
    
    # Convert datetime columns
    for col in df.columns:
        if df[col].dtype == 'object':
            # Try to convert datetime strings
            try:
                df[col] = pd.to_datetime(df[col], errors='ignore')
            except:
                pass
    
    # Save to parquet
    output = io.BytesIO()
    df.to_parquet(output, index=False)
    return output.getvalue()

@router.get("/formats")
async def get_export_formats(
    current_user: Dict[str, Any] = Depends(get_current_active_user)
):
    """Get available export formats and their descriptions"""
    
    formats = {
        "csv": {
            "name": "CSV (Comma Separated Values)",
            "description": "Tabular format, compatible with Excel and most analysis tools",
            "file_extension": ".csv"
        },
        "json": {
            "name": "JSON (JavaScript Object Notation)",
            "description": "Structured format, preserves data hierarchy and types",
            "file_extension": ".json"
        },
        "parquet": {
            "name": "Apache Parquet",
            "description": "Columnar format, optimized for analytics and big data tools",
            "file_extension": ".parquet"
        }
    }
    
    return {"formats": formats}
