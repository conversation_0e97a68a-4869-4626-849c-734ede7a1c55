# PowerShell script for automatic conda environment activation
# Psychiatric ML Data Collection System

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Psychiatric ML Data Collection System" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Check if conda is available
try {
    $condaVersion = conda --version 2>$null
    Write-Host "✓ Conda found: $condaVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Conda is not found in PATH" -ForegroundColor Red
    Write-Host "Please ensure Anaconda/Miniconda is properly installed" -ForegroundColor Yellow
    Write-Host "and added to your system PATH" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if myenv environment exists
$envExists = conda info --envs | Select-String "myenv"
if (-not $envExists) {
    Write-Host "Environment 'myenv' not found. Creating it now..." -ForegroundColor Yellow
    Write-Host "This may take a few minutes..." -ForegroundColor Yellow
    
    try {
        conda env create -f environment.yml
        Write-Host "✓ Environment created successfully!" -ForegroundColor Green
    } catch {
        Write-Host "ERROR: Failed to create conda environment" -ForegroundColor Red
        Write-Host "Please check the environment.yml file and try again" -ForegroundColor Yellow
        Read-Host "Press Enter to exit"
        exit 1
    }
}

# Activate the environment
Write-Host "Activating conda environment 'myenv'..." -ForegroundColor Yellow

# Initialize conda for PowerShell (if not already done)
try {
    conda init powershell 2>$null
} catch {
    Write-Host "Warning: Could not initialize conda for PowerShell" -ForegroundColor Yellow
}

# Activate environment
try {
    conda activate myenv
    Write-Host "✓ Environment activated successfully" -ForegroundColor Green
    Write-Host "Current environment: $env:CONDA_DEFAULT_ENV" -ForegroundColor Green
    
    Write-Host "`nPython version:" -ForegroundColor Cyan
    python --version
    
    Write-Host "`nYou can now run:" -ForegroundColor Cyan
    Write-Host "  python backend/main.py    (to start backend server)" -ForegroundColor White
    Write-Host "  cd frontend && npm start  (to start frontend server)" -ForegroundColor White
    Write-Host ""
    
} catch {
    Write-Host "ERROR: Failed to activate environment" -ForegroundColor Red
    Write-Host "Please try manually: conda activate myenv" -ForegroundColor Yellow
}

# Keep PowerShell open
Write-Host "PowerShell will remain open with the activated environment." -ForegroundColor Green
