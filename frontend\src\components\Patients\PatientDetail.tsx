import React from 'react';
import { useParams } from 'react-router-dom';

const PatientDetail: React.FC = () => {
  const { pid } = useParams<{ pid: string }>();

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-semibold text-gray-900">
        Patient Details: {pid}
      </h1>
      
      <div className="card">
        <div className="card-body">
          <p className="text-gray-600">Patient detail functionality will be implemented here.</p>
        </div>
      </div>
    </div>
  );
};

export default PatientDetail;
