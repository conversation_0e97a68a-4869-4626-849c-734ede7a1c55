# Psychiatric ML System - Backend Debug Guide

## 🚨 Critical System Requirements

### Environment Setup
```bash
# Python Version
python --version  # Must be 3.11+

# Required System Dependencies
pip install fastapi[all] uvicorn[standard] surrealdb pydantic-settings
pip install scikit-learn pandas numpy loguru passlib[argon2] python-jose[cryptography]
pip install python-multipart aiofiles jinja2

# Database Setup
# SurrealDB installation required
curl -sSf https://install.surrealdb.com | sh
```

### Environment Variables (.env)
```bash
# Copy these to your .env file
APP_NAME="Psychiatric ML Data Collection"
DEBUG=true
ENVIRONMENT=development
LOG_LEVEL=DEBUG

SURREALDB_URL=memory://
SURREALDB_NAMESPACE=psychiatric
SURREALDB_DATABASE=research_data

SECRET_KEY=your-secret-key-change-in-production-immediately
CLINIC_SECRET=clinic-specific-encryption-key-change-this-too
JWT_EXPIRE_MINUTES=480

ML_MODELS_PATH=./models
ENABLE_REALTIME_PREDICTIONS=true

CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]
```

## 🔍 Common Startup Issues

### Issue 1: Import Errors
```python
# Error: ModuleNotFoundError: No module named 'surrealdb'
# Solution: Install SurrealDB Python client
pip install surrealdb

# Error: ModuleNotFoundError: No module named 'database.connection'
# Solution: Check your PYTHONPATH and project structure
export PYTHONPATH="${PYTHONPATH}:$(pwd)/backend"
```

### Issue 2: Database Connection Failures
```python
# Error: Connection refused to SurrealDB
# Debug Steps:

# 1. Check if SurrealDB is running
surreal start --log debug memory

# 2. Test connection manually
from surrealdb import Surreal

async def test_db():
    db = Surreal()
    try:
        await db.connect("memory://")
        print("✓ Database connection successful")
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
```

### Issue 3: Pydantic Model Validation Errors
```python
# Error: ValidationError in patient creation
# Common causes:

# 1. PID format validation
# Correct format: PID_[16chars][4checksum]
valid_pid = "PID_AbC123+/XyZ890pQ1234"

# 2. Missing required fields
required_demographics = {
    "pid": "PID_AbC123+/XyZ890pQ1234",
    "age_group": "26-35",
    "gender": "Female", 
    "education": "Bachelor",
    "occupation": "Healthcare",
    "marital_status": "Single",
    "living_situation": "Alone",
    "referral_source": "Self",
    "insurance_type": "Private",
    "clinician_id": "demo_user"
}
```

## 🐛 API Debugging

### Debug Authentication Issues
```python
# Test JWT token generation
from security.auth_manager import AuthManager

auth = AuthManager()
token = auth.create_access_token(data={"sub": "demo", "user_id": "demo_user"})
print(f"Generated token: {token}")

# Verify token
payload = auth.verify_token(token)
print(f"Token payload: {payload}")
```

### Debug Patient Creation
```python
# Test patient creation endpoint
import requests
import json

# 1. Get authentication token
login_data = {"username": "demo", "password": "password"}
auth_response = requests.post("http://localhost:8000/api/v1/auth/login", data=login_data)
token = auth_response.json()["access_token"]

# 2. Create patient
headers = {"Authorization": f"Bearer {token}"}
patient_data = {
    "pid": "PID_AbC123+/XyZ890pQ1234",
    "demographics": {
        "age_group": "26-35",
        "gender": "Female",
        "education": "Bachelor", 
        "occupation": "Healthcare",
        "marital_status": "Single",
        "living_situation": "Alone",
        "referral_source": "Self",
        "insurance_type": "Private"
    },
    "clinician_id": "demo_user"
}

response = requests.post(
    "http://localhost:8000/api/v1/patients/",
    headers=headers,
    json=patient_data
)
print(f"Status: {response.status_code}")
print(f"Response: {response.json()}")
```

### Debug ML Pipeline Issues
```python
# Test ML pipeline initialization
from ml.pipeline import PsychiatricMLPipeline

pipeline = PsychiatricMLPipeline()
print(f"Pipeline initialized: {pipeline is not None}")
print(f"Is trained: {pipeline.is_trained}")

# Test feature extraction
sample_features = {
    "demographics": {
        "age_group": "26-35",
        "gender": "Female",
        "education": "Bachelor"
    },
    "symptoms": {
        "mood_symptoms": {
            "depression": {"present": True, "severity": "Moderate", "duration_weeks": 8}
        }
    }
}

try:
    prediction = pipeline.predict_realtime(sample_features)
    print(f"✓ Prediction successful: {prediction}")
except Exception as e:
    print(f"✗ Prediction failed: {e}")
```

## 📊 Database Debugging

### Check Database Schema
```python
# Connect to SurrealDB and inspect schema
from database.connection import SurrealDBManager

async def debug_database():
    db = SurrealDBManager("memory://")
    await db.connect()
    
    # List tables
    tables = await db.db.query("INFO FOR DB")
    print(f"Database info: {tables}")
    
    # Check patient records
    patients = await db.db.query("SELECT * FROM patients")
    print(f"Patient count: {len(patients) if patients else 0}")
    
    await db.disconnect()
```

### Test PID Security
```python
# Test PID generation and validation
from security.pid_manager import SecurePIDHandler

pid_handler = SecurePIDHandler()

# Generate PID
original_id = "PATIENT_12345"
secure_pid = pid_handler.generate_pid(original_id)
print(f"Generated PID: {secure_pid}")

# Validate PID
is_valid = pid_handler.validate_pid_integrity(secure_pid)
print(f"PID is valid: {is_valid}")

# Generate search token
search_token = pid_handler.generate_search_token(secure_pid)
print(f"Search token: {search_token}")
```

## 🔧 Performance Debugging

### Memory Usage Monitoring
```python
import psutil
import os

def check_memory_usage():
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    print(f"RSS Memory: {memory_info.rss / 1024 / 1024:.2f} MB")
    print(f"VMS Memory: {memory_info.vms / 1024 / 1024:.2f} MB")
    
    # System memory
    system_memory = psutil.virtual_memory()
    print(f"System Memory Usage: {system_memory.percent:.1f}%")
```

### API Response Time Testing
```python
import time
import requests

def test_api_performance():
    base_url = "http://localhost:8000"
    
    # Test endpoints
    endpoints = [
        "/health",
        "/api/v1/auth/login",
        "/api/v1/patients/",
        "/api/v1/ml/models/info"
    ]
    
    for endpoint in endpoints:
        start_time = time.time()
        try:
            response = requests.get(f"{base_url}{endpoint}")
            end_time = time.time()
            print(f"{endpoint}: {(end_time - start_time) * 1000:.2f}ms - Status: {response.status_code}")
        except Exception as e:
            print(f"{endpoint}: ERROR - {e}")
```

## 🚨 Error Code Reference

### HTTP Status Codes
```python
# 400 Bad Request
# - Invalid PID format
# - Missing required fields
# - Invalid data types

# 401 Unauthorized  
# - Invalid or expired JWT token
# - Missing Authorization header

# 403 Forbidden
# - Insufficient permissions (not admin/owner)
# - Attempting to modify locked patient record

# 404 Not Found
# - Patient with PID does not exist
# - ML model not found

# 409 Conflict
# - Patient with PID already exists
# - Duplicate resource creation

# 422 Unprocessable Entity
# - Pydantic validation errors
# - Data format issues

# 423 Locked
# - Patient record is locked and cannot be modified

# 500 Internal Server Error
# - Database connection issues
# - ML pipeline errors
# - Unexpected system errors

# 503 Service Unavailable
# - ML models not trained/loaded
# - Database unavailable
```

### ML Pipeline Error Codes
```python
class MLError:
    MODEL_NOT_TRAINED = "ML001: Models must be trained before making predictions"
    INSUFFICIENT_DATA = "ML002: Insufficient training data (minimum 100 samples required)"
    FEATURE_EXTRACTION_FAILED = "ML003: Failed to extract features from patient data"
    PREDICTION_FAILED = "ML004: Model prediction failed"
    MODEL_SAVE_FAILED = "ML005: Failed to save trained models"
    MODEL_LOAD_FAILED = "ML006: Failed to load trained models"
```

## 🔍 Logging and Debugging

### Enable Debug Logging
```python
# In main.py, add debug middleware
from fastapi import Request
import time

@app.middleware("http")
async def debug_requests(request: Request, call_next):
    start_time = time.time()
    
    # Log request
    logger.debug(f"Request: {request.method} {request.url}")
    
    response = await call_next(request)
    
    # Log response time
    process_time = time.time() - start_time
    logger.debug(f"Response time: {process_time:.3f}s")
    
    return response
```

### Database Query Debugging
```python
# Add to database/connection.py
async def debug_query(self, query: str, params: dict = None):
    logger.debug(f"Executing query: {query}")
    if params:
        logger.debug(f"Query parameters: {params}")
    
    try:
        result = await self.db.query(query, params or {})
        logger.debug(f"Query result count: {len(result) if result else 0}")
        return result
    except Exception as e:
        logger.error(f"Query failed: {e}")
        raise
```

## 🧪 Testing Commands

### Unit Tests
```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run tests
pytest tests/ -v

# Run with coverage
pytest tests/ --cov=backend --cov-report=html
```

### Manual API Testing
```bash
# Health check
curl http://localhost:8000/health

# Login
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=demo&password=password"

# Create patient (replace TOKEN with actual token)
curl -X POST http://localhost:8000/api/v1/patients/ \
  -H "Authorization: Bearer TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"pid": "PID_AbC123+/XyZ890pQ1234", "demographics": {...}, "clinician_id": "demo_user"}'
```

### Database Testing
```bash
# Start SurrealDB with debug logging
surreal start --log debug --user root --pass root memory

# Connect via CLI
surreal sql --conn http://localhost:8000 --user root --pass root --ns psychiatric --db research_data
```

## 🔧 Quick Fixes

### Fix 1: Reset Database
```python
# Clear all data and reinitialize
async def reset_database():
    db = SurrealDBManager("memory://")
    await db.connect()
    
    # Remove all patients
    await db.db.query("DELETE patients")
    
    # Reinitialize schema
    await db._initialize_schema()
    
    await db.disconnect()
    print("Database reset complete")
```

### Fix 2: Regenerate ML Models
```python
# Retrain models from scratch
from ml.pipeline import PsychiatricMLPipeline
from database.connection import SurrealDBManager

async def retrain_models():
    db = SurrealDBManager("memory://")
    await db.connect()
    
    pipeline = PsychiatricMLPipeline()
    
    try:
        X, labels = await pipeline.prepare_training_data(db)
        performance = pipeline.train_models(X, labels)
        model_path = pipeline.save_models()
        print(f"✓ Models retrained successfully: {model_path}")
        print(f"Performance: {performance}")
    except Exception as e:
        print(f"✗ Model retraining failed: {e}")
    
    await db.disconnect()
```

### Fix 3: Clear Authentication Issues
```bash
# Generate new secret key
python -c "import secrets; print(secrets.token_urlsafe(32))"

# Update .env file with new SECRET_KEY
# Restart the application
```

## 📋 System Health Checklist

### ✅ Startup Checklist
- [ ] Python 3.11+ installed
- [ ] All dependencies installed (`pip install -r requirements.txt`)
- [ ] Environment variables configured (`.env` file)
- [ ] SurrealDB accessible (memory:// or file://)
- [ ] Models directory exists (`./models/`)
- [ ] Logs directory exists (`./logs/`)
- [ ] No import errors on startup
- [ ] Health endpoint returns 200

### ✅ Runtime Checklist
- [ ] Authentication working (login returns JWT token)
- [ ] Patient creation successful
- [ ] Database queries execute without errors
- [ ] ML pipeline initializes (even if not trained)
- [ ] API responses under performance requirements
- [ ] Memory usage within limits (<150MB)
- [ ] No critical errors in logs

### ✅ ML Pipeline Checklist
- [ ] Feature encoders initialize successfully
- [ ] Training data can be extracted from database
- [ ] Models can be trained (with sufficient data)
- [ ] Predictions work for sample input
- [ ] Models can be saved and loaded
- [ ] Real-time predictions under 500ms

## 🆘 Emergency Recovery

### If System Won't Start
1. Check Python version: `python --version`
2. Reinstall dependencies: `pip install -r requirements.txt --force-reinstall`
3. Clear Python cache: `find . -type d -name __pycache__ -delete`
4. Reset database: Use reset_database() function above
5. Check environment variables: Verify `.env` file exists and is valid
6. Start with minimal config: Set `DEBUG=true` and `SURREALDB_URL=memory://`

### If Database Issues Persist
1. Switch to file-based SurrealDB: `SURREALDB_URL=file://./data/psychiatric.db`
2. Or use SQLite fallback (requires code modification)
3. Check permissions on data directory
4. Verify SurrealDB installation: `surreal version`

### If ML Pipeline Fails
1. Disable ML temporarily: `ENABLE_REALTIME_PREDICTIONS=false`
2. Clear models directory: `rm -rf ./models/*`
3. Check feature extraction with minimal data
4. Verify scikit-learn version compatibility
5. Test with synthetic training data

---

**Remember**: Always check the logs first! The system uses structured logging with different levels (DEBUG, INFO, WARNING, ERROR). Most issues will be clearly explained in the log output.