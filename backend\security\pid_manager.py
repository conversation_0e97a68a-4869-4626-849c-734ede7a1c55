import hmac
import hashlib
import base64
import binascii
import re
from typing import Optional
from config import settings

class SecurePIDHandler:
    """Military-grade PID security without PII exposure"""
    
    def __init__(self, clinic_secret: Optional[str] = None):
        self.clinic_secret = (clinic_secret or settings.clinic_secret).encode()
        # Unified PID format: PID_[16char_data][4char_checksum]
        self.pid_regex = re.compile(r'^PID_[A-Za-z0-9+/]{16}[A-Za-z0-9]{4}$')
    
    def generate_pid(self, original_id: str, clinic_key: Optional[str] = None) -> str:
        """Generate irreversible pseudonymous ID"""
        key = clinic_key.encode() if clinic_key else self.clinic_secret
        
        # HMAC-SHA256 with clinic-specific key
        hmac_hash = hmac.new(key, original_id.encode(), hashlib.sha256).digest()
        
        # Base64 encoding
        b64_hash = base64.urlsafe_b64encode(hmac_hash).decode('utf-8').rstrip('=')
        
        # Take first 16 characters
        data_part = b64_hash[:16]
        
        # Add CRC32 checksum
        checksum = format(binascii.crc32(data_part.encode()) & 0xffffffff, '08X')[-4:]
        
        # Format: PID_[16char_hash][4char_checksum]
        return f"PID_{data_part}{checksum}"
    
    def generate_search_token(self, pid: str) -> str:
        """Generate searchable token for database queries"""
        # Double-hashing prevents rainbow table attacks
        first_hash = hmac.new(self.clinic_secret, pid.encode(), hashlib.sha256).digest()
        search_token = hashlib.sha256(first_hash).hexdigest()[:16]
        return f"ST_{search_token}"
    
    def validate_pid_integrity(self, pid: str) -> bool:
        """Validate PID format and checksum without decryption"""
        # Use unified regex
        if not self.pid_regex.match(pid):
            return False
        
        # Verify CRC32 checksum
        data_part = pid[4:-4]  # Remove PID_ prefix and checksum suffix
        checksum_part = pid[-4:]
        
        expected_checksum = format(binascii.crc32(data_part.encode()) & 0xffffffff, '08X')[-4:]
        return checksum_part == expected_checksum
