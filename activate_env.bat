@echo off
REM Automatic conda environment activation script for Psychiatric ML Project

echo ========================================
echo Psychiatric ML Data Collection System
echo ========================================

REM Check if conda is available
where conda >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Conda is not found in PATH
    echo Please ensure Anaconda/Miniconda is properly installed
    echo and added to your system PATH
    pause
    exit /b 1
)

REM Check if myenv environment exists
conda info --envs | findstr "myenv" >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Environment 'myenv' not found. Creating it now...
    echo This may take a few minutes...
    conda env create -f environment.yml
    if %ERRORLEVEL% NEQ 0 (
        echo ERROR: Failed to create conda environment
        echo Please check the environment.yml file and try again
        pause
        exit /b 1
    )
    echo Environment created successfully!
)

REM Activate the environment
echo Activating conda environment 'myenv'...
call conda activate myenv

REM Verify activation
if "%CONDA_DEFAULT_ENV%"=="myenv" (
    echo ✓ Environment activated successfully
    echo Current environment: %CONDA_DEFAULT_ENV%
    echo Python version:
    python --version
    echo.
    echo You can now run:
    echo   python backend/main.py    (to start backend server)
    echo   cd frontend && npm start  (to start frontend server)
    echo.
) else (
    echo ERROR: Failed to activate environment
    echo Please try manually: conda activate myenv
)

REM Keep the window open
cmd /k
