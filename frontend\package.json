{"name": "psychiatric-ml-frontend", "version": "1.0.0", "description": "Frontend for Psychiatric ML Data Collection System", "private": true, "dependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "typescript": "^5.3.3", "web-vitals": "^3.5.0", "axios": "^1.6.2", "react-hook-form": "^7.48.2", "react-query": "^3.39.3", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "date-fns": "^2.30.0", "recharts": "^2.8.0"}, "devDependencies": {"tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@tailwindcss/forms": "^0.5.7"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8000"}