from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Create FastAPI application
app = FastAPI(
    title="Psychiatric ML Data Collection",
    description="High-performance, privacy-first psychiatric data collection system with ML capabilities",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": "2024-01-01T00:00:00Z",
        "version": "1.0.0",
        "environment": "development",
        "database": "ready",
        "ml_models": "ready"
    }

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Psychiatric ML Data Collection System API",
        "version": "1.0.0",
        "docs_url": "/docs",
        "health_check": "/health",
        "status": "operational"
    }

# Demo authentication endpoint
@app.post("/api/v1/auth/login")
async def login():
    """Demo login endpoint"""
    return {
        "access_token": "demo_token_12345",
        "token_type": "bearer",
        "message": "Login successful (demo mode)"
    }

# Demo patients endpoint
@app.get("/api/v1/patients")
async def get_patients():
    """Demo patients endpoint"""
    return {
        "patients": [
            {"id": 1, "name": "Demo Patient 1", "status": "active"},
            {"id": 2, "name": "Demo Patient 2", "status": "active"}
        ],
        "total": 2,
        "message": "Demo data - replace with real implementation"
    }

# Demo ML predictions endpoint
@app.post("/api/v1/ml/predict")
async def predict():
    """Demo ML prediction endpoint"""
    return {
        "prediction": {
            "risk_score": 0.25,
            "confidence": 0.85,
            "recommendations": ["Regular monitoring", "Stress management"]
        },
        "model_version": "demo_v1.0",
        "message": "Demo prediction - replace with real ML model"
    }

# Demo export endpoint
@app.get("/api/v1/exports/data")
async def export_data():
    """Demo export endpoint"""
    return {
        "export_url": "/downloads/demo_export.csv",
        "format": "CSV",
        "records": 100,
        "message": "Demo export - replace with real implementation"
    }

if __name__ == "__main__":
    # Development server
    uvicorn.run(
        "main_simple:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
        access_log=True
    )
