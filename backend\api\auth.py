from fastapi import <PERSON><PERSON>out<PERSON>, HTTPException, Depends, status
from fastapi.security import OAuth2PasswordRequestForm
from pydantic import BaseModel
from typing import Dict, Any
from datetime import timedelta
from security.auth_manager import authenticate_user, auth_manager, get_current_active_user
from utils.logging import logger

router = APIRouter(prefix="/auth", tags=["authentication"])

class Token(BaseModel):
    access_token: str
    token_type: str
    expires_in: int
    user_info: Dict[str, Any]

class UserInfo(BaseModel):
    username: str
    user_id: str
    full_name: str
    roles: list

@router.post("/login", response_model=Token)
async def login(form_data: OAuth2PasswordRequestForm = Depends()):
    """Authenticate user and return JWT token"""
    
    # Authenticate user
    user = authenticate_user(form_data.username, form_data.password)
    if not user:
        logger.warning(f"Failed login attempt for username: {form_data.username}", extra={"security": True})
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Create access token
    access_token_expires = timedelta(minutes=auth_manager.access_token_expire_minutes)
    access_token = auth_manager.create_access_token(
        data={"sub": user["username"]}, 
        expires_delta=access_token_expires
    )
    
    logger.info(f"Successful login for user: {user['username']}", extra={"security": True})
    
    return Token(
        access_token=access_token,
        token_type="bearer",
        expires_in=auth_manager.access_token_expire_minutes * 60,
        user_info={
            "username": user["username"],
            "user_id": user["user_id"],
            "full_name": user["full_name"],
            "roles": user["roles"]
        }
    )

@router.get("/me", response_model=UserInfo)
async def get_current_user_info(current_user: Dict[str, Any] = Depends(get_current_active_user)):
    """Get current user information"""
    return UserInfo(
        username=current_user["username"],
        user_id=current_user["user_id"],
        full_name=current_user["full_name"],
        roles=current_user["roles"]
    )

@router.post("/logout")
async def logout(current_user: Dict[str, Any] = Depends(get_current_active_user)):
    """Logout user (client-side token removal)"""
    logger.info(f"User logout: {current_user['username']}", extra={"security": True})
    return {"message": "Successfully logged out"}

@router.get("/validate")
async def validate_token(current_user: Dict[str, Any] = Depends(get_current_active_user)):
    """Validate current token"""
    return {"valid": True, "user": current_user["username"]}
