import React from 'react';
import { useQuery } from 'react-query';
import { useAuth } from '../../contexts/AuthContext';
import { apiService } from '../../services/api';
import LoadingSpinner from '../UI/LoadingSpinner';

const Dashboard: React.FC = () => {
  const { user } = useAuth();

  // Fetch health status
  const { data: healthData, isLoading: healthLoading } = useQuery(
    'health',
    () => apiService.healthCheck(),
    {
      refetchInterval: 30000, // Refetch every 30 seconds
    }
  );

  // Fetch model status
  const { data: modelStatus, isLoading: modelLoading } = useQuery(
    'modelStatus',
    () => apiService.getModelStatus(),
    {
      refetchInterval: 60000, // Refetch every minute
    }
  );

  // Fetch recent patients
  const { data: patientsData, isLoading: patientsLoading } = useQuery(
    'recentPatients',
    () => apiService.searchPatients(),
    {
      refetchInterval: 30000,
    }
  );

  const stats = [
    {
      name: 'Total Patients',
      value: patientsData?.total || 0,
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
      ),
      color: 'bg-blue-500',
    },
    {
      name: 'ML Models',
      value: modelStatus?.is_trained ? 'Trained' : 'Not Trained',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      color: modelStatus?.is_trained ? 'bg-green-500' : 'bg-yellow-500',
    },
    {
      name: 'System Status',
      value: healthData?.status === 'healthy' ? 'Healthy' : 'Issues',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      color: healthData?.status === 'healthy' ? 'bg-green-500' : 'bg-red-500',
    },
    {
      name: 'Database',
      value: healthData?.database === 'connected' ? 'Connected' : 'Disconnected',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
        </svg>
      ),
      color: healthData?.database === 'connected' ? 'bg-green-500' : 'bg-red-500',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Welcome header */}
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">
          Welcome back, {user?.full_name}
        </h1>
        <p className="mt-1 text-sm text-gray-600">
          Here's what's happening with your psychiatric data collection system today.
        </p>
      </div>

      {/* Stats grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <div key={stat.name} className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`${stat.color} rounded-md p-3 text-white`}>
                    {stat.icon}
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      {stat.name}
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {stat.value}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Recent activity and system info */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Recent Patients */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Recent Patients
            </h3>
          </div>
          <div className="card-body">
            {patientsLoading ? (
              <LoadingSpinner />
            ) : (
              <div className="space-y-3">
                {patientsData?.patients?.slice(0, 5).map((patient) => (
                  <div key={patient.pid} className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {patient.pid}
                      </p>
                      <p className="text-sm text-gray-500">
                        Created: {new Date(patient.created_at).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex items-center">
                      <span className={`badge ${
                        patient.completeness_score > 0.8 ? 'badge-success' :
                        patient.completeness_score > 0.5 ? 'badge-warning' : 'badge-danger'
                      }`}>
                        {Math.round(patient.completeness_score * 100)}% complete
                      </span>
                    </div>
                  </div>
                )) || (
                  <p className="text-sm text-gray-500">No patients found</p>
                )}
              </div>
            )}
          </div>
        </div>

        {/* System Information */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              System Information
            </h3>
          </div>
          <div className="card-body">
            {healthLoading || modelLoading ? (
              <LoadingSpinner />
            ) : (
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Application</h4>
                  <p className="text-sm text-gray-600">
                    Version: {healthData?.version || 'Unknown'}
                  </p>
                  <p className="text-sm text-gray-600">
                    Environment: {healthData?.environment || 'Unknown'}
                  </p>
                </div>
                
                {modelStatus && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">ML Models</h4>
                    <p className="text-sm text-gray-600">
                      Status: {modelStatus.is_trained ? 'Trained' : 'Not Trained'}
                    </p>
                    {modelStatus.training_metadata?.training_date && (
                      <p className="text-sm text-gray-600">
                        Last Training: {new Date(modelStatus.training_metadata.training_date).toLocaleDateString()}
                      </p>
                    )}
                    <p className="text-sm text-gray-600">
                      Features: {modelStatus.feature_count || 0}
                    </p>
                  </div>
                )}

                <div>
                  <h4 className="text-sm font-medium text-gray-900">Performance</h4>
                  {modelStatus?.model_performance && Object.keys(modelStatus.model_performance).length > 0 ? (
                    <div className="space-y-1">
                      {Object.entries(modelStatus.model_performance).map(([key, value]) => (
                        <p key={key} className="text-sm text-gray-600">
                          {key}: {typeof value === 'number' ? value.toFixed(3) : value}
                        </p>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-600">No performance metrics available</p>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Quick Actions
          </h3>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <a
              href="/patients/create"
              className="btn btn-primary text-center"
            >
              Add New Patient
            </a>
            <a
              href="/patients"
              className="btn btn-outline text-center"
            >
              View All Patients
            </a>
            {user?.roles?.includes('researcher') && (
              <a
                href="/ml"
                className="btn btn-outline text-center"
              >
                ML Analytics
              </a>
            )}
            {user?.roles?.includes('researcher') && (
              <a
                href="/export"
                className="btn btn-outline text-center"
              >
                Export Data
              </a>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
