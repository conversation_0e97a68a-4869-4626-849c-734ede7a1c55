from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import uvicorn
import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import settings
from utils.logging import setup_logging, logger
from database.connection import SurrealDBManager
from api import auth, patients, ml_predictions, exports
import dependencies

# Global database manager
db_manager = None
ml_pipeline = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global db_manager, ml_pipeline

    # Startup
    logger.info("Starting Psychiatric ML Data Collection System...")

    # Initialize logging
    setup_logging()

    # Initialize database
    try:
        db_manager = SurrealDBManager()
        await db_manager.connect()
        logger.info("✓ Database connection established")
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        raise

    # Initialize ML pipeline (load models if they exist)
    try:
        from ml.pipeline import PsychiatricMLPipeline
        ml_pipeline = PsychiatricMLPipeline()
        try:
            ml_pipeline.load_models(str(ml_pipeline.models_path))
            logger.info("✓ ML models loaded successfully")
        except:
            logger.info("No pre-trained models found - will train on first use")
    except Exception as e:
        logger.warning(f"ML pipeline initialization warning: {e}")

    # Set global instances in dependencies module
    dependencies.set_global_instances(db_manager, ml_pipeline)

    logger.info("✓ Application startup completed")

    yield

    # Shutdown
    logger.info("Shutting down application...")

    if db_manager:
        await db_manager.disconnect()
        logger.info("✓ Database connection closed")

    logger.info("✓ Application shutdown completed")

# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    description="High-performance, privacy-first psychiatric data collection system with ML capabilities",
    version="1.0.0",
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
    lifespan=lifespan
)

# Security middleware
app.add_middleware(
    TrustedHostMiddleware, 
    allowed_hosts=["localhost", "127.0.0.1", "*.localhost"] if settings.debug else ["your-domain.com"]
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler for unhandled errors"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    
    if settings.debug:
        return JSONResponse(
            status_code=500,
            content={
                "detail": f"Internal server error: {str(exc)}",
                "type": type(exc).__name__
            }
        )
    else:
        return JSONResponse(
            status_code=500,
            content={"detail": "Internal server error"}
        )

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Check database connection
        db_status = "connected" if db_manager and db_manager.connected else "disconnected"
        
        # Check ML models
        from api.ml_predictions import ml_pipeline
        ml_status = "loaded" if ml_pipeline.is_trained else "not_loaded"
        
        return {
            "status": "healthy",
            "timestamp": "2024-01-01T00:00:00Z",  # Would use datetime.now() in real app
            "version": "1.0.0",
            "environment": settings.environment,
            "database": db_status,
            "ml_models": ml_status
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e)
            }
        )

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Psychiatric ML Data Collection System API",
        "version": "1.0.0",
        "docs_url": "/docs" if settings.debug else "Documentation not available in production",
        "health_check": "/health",
        "api_prefix": settings.api_v1_prefix
    }

# Include API routers
app.include_router(auth.router, prefix=settings.api_v1_prefix)
app.include_router(patients.router, prefix=settings.api_v1_prefix)
app.include_router(ml_predictions.router, prefix=settings.api_v1_prefix)
app.include_router(exports.router, prefix=settings.api_v1_prefix)

# Request logging middleware
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log all HTTP requests"""
    start_time = "2024-01-01T00:00:00Z"  # Would use time.time() in real app
    
    # Log request
    logger.info(
        f"Request: {request.method} {request.url.path} "
        f"from {request.client.host if request.client else 'unknown'}"
    )
    
    # Process request
    response = await call_next(request)
    
    # Log response
    process_time = 0.1  # Would calculate actual time in real app
    logger.info(
        f"Response: {response.status_code} "
        f"({process_time:.3f}s)"
    )
    
    return response

if __name__ == "__main__":
    # Development server
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
        access_log=True
    )
