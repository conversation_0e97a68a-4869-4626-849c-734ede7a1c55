from pydantic import BaseModel, Field, model_validator, validator
from typing import Dict, List, Optional, Literal, Union, Any, Tuple
from datetime import datetime
from enum import Enum

class PatientDemographics(BaseModel):
    pid: str = Field(..., pattern=r'^PID_[A-Za-z0-9+/]{16}[A-Za-z0-9]{4}$')
    
    # Categorical fields optimized for ML
    age_group: Literal["18-25", "26-35", "36-45", "46-55", "56-65", "65+"]
    gender: Literal["Male", "Female", "Non-binary", "Other", "Undisclosed"]
    education: Literal["Primary", "Secondary", "Bachelor", "Graduate", "Professional"]
    occupation: Literal["Healthcare", "Education", "Technology", "Service", "Manual", "Student", "Unemployed", "Retired", "Other"]
    marital_status: Literal["Single", "Married", "Divorced", "Widowed", "Separated"]
    living_situation: Literal["Alone", "Family", "Shared", "Assisted", "Institutional"]
    
    # Clinical context
    referral_source: Literal["Self", "GP", "Specialist", "Emergency", "Court", "Other"]
    insurance_type: Literal["Private", "Public", "Uninsured", "Unknown"]
    
    # Metadata
    created_at: Optional[datetime] = Field(default_factory=datetime.now)
    clinician_id: str
    data_quality_score: float = Field(default=1.0, ge=0.0, le=1.0)

class SymptomItem(BaseModel):
    present: bool = False
    severity: Optional[Literal["Mild", "Moderate", "Severe"]] = None
    duration_weeks: Optional[int] = None
    functional_impact: Optional[Literal["None", "Mild", "Moderate", "Severe"]] = None
    
    @model_validator(mode='after')
    def validate_symptom_logic(self):
        if not self.present:
            self.severity = None
            self.duration_weeks = None
            self.functional_impact = None
        return self

class SymptomAssessment(BaseModel):
    """DSM-5-TR aligned symptom assessment"""
    pid: str
    assessment_date: datetime = Field(default_factory=datetime.now)
    
    # Major symptom domains (each with severity + duration)
    mood_symptoms: Dict[str, SymptomItem]
    psychotic_symptoms: Dict[str, SymptomItem]  
    anxiety_symptoms: Dict[str, SymptomItem]
    cognitive_symptoms: Dict[str, SymptomItem]
    behavioral_symptoms: Dict[str, SymptomItem]
    
    # Global assessment
    functioning_level: int = Field(..., ge=1, le=100)  # GAF-style score
    symptom_severity_index: float = Field(..., ge=0.0, le=10.0)

class SubstanceUseItem(BaseModel):
    current_use: bool = False
    frequency: Optional[Literal["Daily", "Weekly", "Monthly", "Occasional"]] = None
    severity: Optional[Literal["Mild", "Moderate", "Severe"]] = None
    years_of_use: Optional[int] = None

class ClinicalHistory(BaseModel):
    pid: str

    # Psychiatric history
    previous_diagnoses: List[str] = []  # ICD-11 codes
    hospitalization_count: int = 0
    suicide_attempts: int = 0
    medication_trials: Dict[str, str] = {}  # drug_class -> response
    therapy_history: List[str] = []  # CBT, DBT, etc.

    # Family & genetic factors
    family_psychiatric_history: bool = False
    family_conditions: List[str] = []

    # Substance use (structured for ML)
    substance_use: Dict[str, SubstanceUseItem]

    # Medical comorbidities
    medical_conditions: List[str] = []
    current_medications: List[str] = []

    # Social determinants
    trauma_history: bool = False
    social_support_level: Literal["High", "Moderate", "Low", "Minimal"]
    housing_stability: Literal["Stable", "Temporary", "Unstable", "Homeless"]

class LabResults(BaseModel):
    """Structured lab data with ML preprocessing"""
    pid: str
    collection_date: datetime
    lab_type: Literal["Basic_Metabolic", "CBC", "Liver_Function", "Thyroid", "Toxicology", "Inflammatory_Markers"]

    # Raw values with units
    raw_values: Dict[str, Optional[float]]
    # Strengthened reference ranges validation
    reference_ranges: Dict[str, Tuple[float, float]]

    # ML-ready features
    normalized_values: Dict[str, Optional[float]] = {}
    abnormal_flags: Dict[str, int] = {}
    clinical_significance: Dict[str, bool] = {}

    @model_validator(mode='after')
    def compute_ml_features(self):
        """Auto-compute derived features for ML"""
        for param, value in self.raw_values.items():
            if value is not None and param in self.reference_ranges:
                # Safely unpack the validated tuple
                low, high = self.reference_ranges[param]

                # Z-score normalization
                mid = (low + high) / 2
                std = (high - low) / 4  # Assume 2-sigma range
                self.normalized_values[param] = (value - mid) / std

                # Abnormal flags
                if value < low:
                    self.abnormal_flags[param] = -1
                elif value > high:
                    self.abnormal_flags[param] = 1
                else:
                    self.abnormal_flags[param] = 0

                # Clinical significance (customize per lab type)
                self.clinical_significance[param] = abs(self.normalized_values[param]) > 2.0

        return self
