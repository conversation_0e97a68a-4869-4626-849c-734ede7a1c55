import React, { createContext, useContext, useState, ReactNode } from 'react';
import { AlertState } from '../types';

interface AlertContextType {
  alert: AlertState | null;
  showAlert: (type: AlertState['type'], message: string, duration?: number) => void;
  hideAlert: () => void;
}

const AlertContext = createContext<AlertContextType | undefined>(undefined);

export const useAlert = (): AlertContextType => {
  const context = useContext(AlertContext);
  if (!context) {
    throw new Error('useAlert must be used within an AlertProvider');
  }
  return context;
};

interface AlertProviderProps {
  children: ReactNode;
}

export const AlertProvider: React.FC<AlertProviderProps> = ({ children }) => {
  const [alert, setAlert] = useState<AlertState | null>(null);

  const showAlert = (type: AlertState['type'], message: string, duration: number = 5000) => {
    setAlert({
      type,
      message,
      visible: true,
    });

    // Auto-hide after duration
    if (duration > 0) {
      setTimeout(() => {
        hideAlert();
      }, duration);
    }
  };

  const hideAlert = () => {
    setAlert(null);
  };

  const value: AlertContextType = {
    alert,
    showAlert,
    hideAlert,
  };

  return (
    <AlertContext.Provider value={value}>
      {children}
      {alert && alert.visible && (
        <div className="fixed top-4 right-4 z-50 max-w-sm w-full">
          <div className={`alert alert-${alert.type} shadow-lg`}>
            <div className="flex items-center justify-between">
              <span>{alert.message}</span>
              <button
                onClick={hideAlert}
                className="ml-4 text-current hover:text-opacity-75 focus:outline-none"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}
    </AlertContext.Provider>
  );
};
