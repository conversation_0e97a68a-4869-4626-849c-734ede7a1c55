pyarrow-21.0.0.dist-info/DELVEWHEEL,sha256=QbiJjcrDPn-oPeGnWc1im2P3MJldaXxbt7pmpUcSOqg,239
pyarrow-21.0.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyarrow-21.0.0.dist-info/LICENSE.txt,sha256=MguNxS6AronnaltbJGy1wnQjSDNj2CR5ucWceErRtv4,115734
pyarrow-21.0.0.dist-info/METADATA,sha256=BaHZ-HfQ7DgcFord1kkddnnhu3bm57TpmT_n47Dvq_8,3373
pyarrow-21.0.0.dist-info/NOTICE.txt,sha256=fgreEJd1fnOJknCZAjAJjKJmhwG7e2KDGVa1y7xhIDM,2997
pyarrow-21.0.0.dist-info/RECORD,,
pyarrow-21.0.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyarrow-21.0.0.dist-info/WHEEL,sha256=nA5ktGoXpQ3tidNN40L_vJmusG_zILW-M5MSKaudqZM,101
pyarrow-21.0.0.dist-info/top_level.txt,sha256=Zuk_c1WeinXdMz20fXlEtGC67zfKOWuwU8adpEEU_nI,18
pyarrow.libs/msvcp140-a118642f3ae8774fb9dc223e15c4a52e.dll,sha256=pUKfCOyeSxLIn_tGHY2YhJYBC3WFD3OYx02QjzvH1ms,576128
pyarrow/__init__.pxd,sha256=eC5b2a7fm-SmomDxOM02JS_5jrwnBTFc1tzxA7nLrYM,2237
pyarrow/__init__.py,sha256=Ro0s0baNRirjQjNAWlN6nZhwwWqTHf5wS9KRC6nuC_8,19217
pyarrow/__pycache__/__init__.cpython-313.pyc,,
pyarrow/__pycache__/_compute_docstrings.cpython-313.pyc,,
pyarrow/__pycache__/_generated_version.cpython-313.pyc,,
pyarrow/__pycache__/acero.cpython-313.pyc,,
pyarrow/__pycache__/benchmark.cpython-313.pyc,,
pyarrow/__pycache__/cffi.cpython-313.pyc,,
pyarrow/__pycache__/compute.cpython-313.pyc,,
pyarrow/__pycache__/conftest.cpython-313.pyc,,
pyarrow/__pycache__/csv.cpython-313.pyc,,
pyarrow/__pycache__/cuda.cpython-313.pyc,,
pyarrow/__pycache__/dataset.cpython-313.pyc,,
pyarrow/__pycache__/feather.cpython-313.pyc,,
pyarrow/__pycache__/flight.cpython-313.pyc,,
pyarrow/__pycache__/fs.cpython-313.pyc,,
pyarrow/__pycache__/ipc.cpython-313.pyc,,
pyarrow/__pycache__/json.cpython-313.pyc,,
pyarrow/__pycache__/jvm.cpython-313.pyc,,
pyarrow/__pycache__/orc.cpython-313.pyc,,
pyarrow/__pycache__/pandas_compat.cpython-313.pyc,,
pyarrow/__pycache__/substrait.cpython-313.pyc,,
pyarrow/__pycache__/types.cpython-313.pyc,,
pyarrow/__pycache__/util.cpython-313.pyc,,
pyarrow/_acero.cp313-win_amd64.pyd,sha256=Zagxp3xENO0n9ECUFWIFOXS9fWjD6wKhav_9AammB2k,198656
pyarrow/_acero.pxd,sha256=rjCRAq1oo6OyT0c1ISd3_RYOLmo99UEhk88J5Efn730,1484
pyarrow/_acero.pyx,sha256=NeURFVoUcbmUm1OtlXTNt5nNLf3gnGTTRWYxPRA44ZU,22148
pyarrow/_azurefs.pyx,sha256=aWzXkChYc18FEdyry5i7OIV1LABJvj99Gf1JphgGdfc,9336
pyarrow/_compute.cp313-win_amd64.pyd,sha256=I7lvzTMQMf8qXwroZLY6jlXj1nQ2JkSng8yyZ_l2vGU,879616
pyarrow/_compute.pxd,sha256=GQUnCeC9vy9YEnT3gHKBoQMrJvjjmjdCQuIYp6fXXio,2094
pyarrow/_compute.pyx,sha256=s_4JsA1ysoq6LaMqabYP9wiYZpPIeLQZ2k5JdskVucg,115570
pyarrow/_compute_docstrings.py,sha256=2KRhcMXk12GS_zihOVWsIjyU9tgPBpPNHUi-GpQPZ6I,1763
pyarrow/_csv.cp313-win_amd64.pyd,sha256=6rDJpHalUREHDmNxebpV4K_KTzhouVl_SsrEgPC7zbY,263168
pyarrow/_csv.pxd,sha256=rI6eCBX4P1-C6wwhRySHK3vWvjy2dorVeAPaSdROeZQ,1693
pyarrow/_csv.pyx,sha256=O13jduAjcJ-rUS14IrUR_B5ewlw6pI5tZVzjMfKo838,56219
pyarrow/_cuda.pxd,sha256=S9wJXbxxM8MO-oAgWHkblKMdQr9zuGoNfKGkIKVXw0c,1989
pyarrow/_cuda.pyx,sha256=VuIJOfJya-vVEVjjHk-xtfaYnc35jJFooGSJ_iTCGPI,36168
pyarrow/_dataset.cp313-win_amd64.pyd,sha256=o7q_KSu-RgGvqwflu20rLtgWjEbo_ilzGeuL50bEvyU,719872
pyarrow/_dataset.pxd,sha256=yakY7xxTWXmy6DCnXqxp2BH2X5_LzFb1Aj8p4eRJHEM,5127
pyarrow/_dataset.pyx,sha256=f11mzfPVGamcJHz5LrJD5G4BmgBqir0HJxu4Ntn9Myg,166963
pyarrow/_dataset_orc.cp313-win_amd64.pyd,sha256=-4sXm-kWuRPRzOywWZN5wNnDOvgOr10jHnolYDRAOZE,61952
pyarrow/_dataset_orc.pyx,sha256=vgB9AQ_DjKvXddUNoksC2HqtlzLNmi-dqTEncd6DrCQ,1550
pyarrow/_dataset_parquet.cp313-win_amd64.pyd,sha256=MRyijm_vkrJnfuYF8Dcit_xoY3tAKbvVYmeK-5pN_Zc,263168
pyarrow/_dataset_parquet.pxd,sha256=ayCJyRcMOIVVKgtCYF2SgdGKHrTi-jfCrgJcDJMW4Mo,1615
pyarrow/_dataset_parquet.pyx,sha256=pk6sJ068LHSCpfPYcqMTOKloG4kb9Q1LGc_D-J3wBT4,42614
pyarrow/_dataset_parquet_encryption.cp313-win_amd64.pyd,sha256=HVstdiMFrbELS6wXy9meomg9bBSBNyIJUWVkUT2A97s,84992
pyarrow/_dataset_parquet_encryption.pyx,sha256=q5FVop8IERFxVChOUnHxplGjdVs9nR2o-S47dUARWEU,7407
pyarrow/_dlpack.pxi,sha256=wMZLifeA03bfnmKcbMfkkkBlCi2hhdLo2q6kg0IL6-0,1878
pyarrow/_feather.cp313-win_amd64.pyd,sha256=3t_yRkoW25s5nJjO9PRIUkCzlXnt53XBAqYsL-589g0,84480
pyarrow/_feather.pyx,sha256=uMhjMFDUvcfTVBaPCoydbmcRtbzP7Rz6C3v7T9bahGc,3890
pyarrow/_flight.cp313-win_amd64.pyd,sha256=sB0sqh7wKQcAdeFzmZMdyL2zjwkOSyUbUVi6p4istpw,869888
pyarrow/_flight.pyx,sha256=MqL3qpxeHdCp1mSh2uenTHAlt8zE54KLP9NvWpsE4k0,116819
pyarrow/_fs.cp313-win_amd64.pyd,sha256=iO6Cq9VGa-7CxFoVrVFrm-F0qUmn5spGzXSO-6KBQKE,367616
pyarrow/_fs.pxd,sha256=GckaT7VHRMB0si-WFHIfpLAcs5XjOLbbD1zSoI3KwdQ,2530
pyarrow/_fs.pyx,sha256=QObNzk3uSon-hCZSFdkWNPayp7_zyyc_s59qxjl1Dy4,54905
pyarrow/_gcsfs.cp313-win_amd64.pyd,sha256=YqnXrJhnruj1JR59GzpBDM1f84_B7y4SCPAA4QvKIhA,88576
pyarrow/_gcsfs.pyx,sha256=AcIEmYc0KRuyjMWFwzNN-iFcanzBvtwv2zVJnZRoM_I,9255
pyarrow/_generated_version.py,sha256=63aYfnhv5-MVmkZo7Fanz_0VBGBaCHe3-tlYiUaaPPI,534
pyarrow/_hdfs.cp313-win_amd64.pyd,sha256=VPaqK2XY4lVQBaaSkBNp4pNIQo7uVzoVFL8vxoIhpzQ,87552
pyarrow/_hdfs.pyx,sha256=LG5GXt5UvoZTKEKmJVTDNZ7j2HJHfyhq-3CvI4VNfEs,5959
pyarrow/_json.cp313-win_amd64.pyd,sha256=WRmgVSsNFR6fzoKvCKj1lvUN3PINPsgYvY_ubJdvgcw,97280
pyarrow/_json.pxd,sha256=jAfiARHEM1qJ2mAuLPa_KrKAbU5YNFLWD5Rhj4P5c1U,1242
pyarrow/_json.pyx,sha256=TUjPlO37sRoCTdszx3m8AHKaGX9ubTQCM4Bnqlh11lI,12918
pyarrow/_orc.cp313-win_amd64.pyd,sha256=vp8QG-qFcxl6qq4AfDB56a_xS0ZqbPvGsnJd8ni9P3I,150528
pyarrow/_orc.pxd,sha256=G5jSZUTqyt_stuKn0y56L9lyG8oRqf8NzMtzYSD1qaw,5823
pyarrow/_orc.pyx,sha256=ZfgbW4OQnYshV8nQQCXculGpbmLGsKwzwRwNY59QWq8,16001
pyarrow/_parquet.cp313-win_amd64.pyd,sha256=9fiib-REO5mifkk5cSQycnEmgYzH68Kd-5lQBEXZhG4,451072
pyarrow/_parquet.pxd,sha256=iPazzz1RZtvCaQPNea39jVrpMXIHOebQGXIahO8GTyE,5040
pyarrow/_parquet.pyx,sha256=MIfUuFmCvi0nLmXyKd9RBFsZrJsoVU8HnB1jC88t4Zg,82015
pyarrow/_parquet_encryption.cp313-win_amd64.pyd,sha256=uJ7o-HkdnYxcltMhqOlekcUZxOprGqxxNQ8qyw5ODm8,177152
pyarrow/_parquet_encryption.pxd,sha256=WnrnVuKMmf7MhxDESBulUuWztko4HeNdadkOve_pCLQ,2642
pyarrow/_parquet_encryption.pyx,sha256=xqDf0isAOHmU8Q60h_3bHYkS9WsXY07KYDi9vbHgMVo,19576
pyarrow/_pyarrow_cpp_tests.cp313-win_amd64.pyd,sha256=x821FLsru1IgKsSZQ3UD4dk2h8d3m7bBL4ElFEmGK-o,66560
pyarrow/_pyarrow_cpp_tests.pxd,sha256=L_cgXkO1M54NUhRpzHPqLA3-h5qkqTjcxt_OfSlF01I,1232
pyarrow/_pyarrow_cpp_tests.pyx,sha256=cXM3tGxJyLqr-U2oqTKzdWVEsr7d7FLbrSzjw6Aagl0,1815
pyarrow/_s3fs.cp313-win_amd64.pyd,sha256=kYDA50YTKI6oXNd7gE1zwuohPGs209_2thEJmoDvbOg,162304
pyarrow/_s3fs.pyx,sha256=3Br8L0UNH235QLgiKILQRoHzp0OHohoTIwNeeKZnxLo,21086
pyarrow/_substrait.cp313-win_amd64.pyd,sha256=2a5TBwUlgXiD970zEscR_x3Xgeee4L5eLa2bqNGJCx8,145920
pyarrow/_substrait.pyx,sha256=tOdpG8sK4oKgct6ymgQeu9bj_DD2Gb3vFRXrvSg_qOs,16147
pyarrow/acero.py,sha256=nA-41IdBmQuT__dngBqCLBCt-PejkB4Jxu_oNWhzFlQ,16161
pyarrow/array.pxi,sha256=UkCjCGEHS1v56e3RWvJk6w3-Sw5zl2cydLhQ7YLnsOE,163806
pyarrow/arrow.dll,sha256=Mw_l35LDzKvUXNjl1zK38ZtwTxgSXmw_i7Y0UVoURVU,21367296
pyarrow/arrow.lib,sha256=YDSg2LfEDhnkDsrgOiCCB6uCCBadkGJ43I6Y3mInqLw,4070906
pyarrow/arrow_acero.dll,sha256=vdilxFaPb4AnGGYxgozxP1sBYCCVWqdjr3fzQHQhlY4,1268736
pyarrow/arrow_acero.lib,sha256=wb_MXW2cPATnMgC_gW6ov-OFJQjJBhBV8g8ydZWx-zw,287988
pyarrow/arrow_compute.dll,sha256=HbOClthErS0R31EKRx4dY9nar_af5du9x9PZNG0KqCw,10300928
pyarrow/arrow_compute.lib,sha256=LS4nAFW4gojrzYa-PbHZvsF4K5yXpi94_1q3mu5utjI,170946
pyarrow/arrow_dataset.dll,sha256=5c0bpkgqOSo0Kn1MZBgO0J6vwRkLweZsmFrN27co260,1327616
pyarrow/arrow_dataset.lib,sha256=TlgEULy-BgbggSj8ey6vgNxlFEvEEBZvWbwwqh69a1U,413512
pyarrow/arrow_flight.dll,sha256=6DawZrCtILJYPdVT4dixau9-UU9uqLte9Vfi16JCG_g,10711040
pyarrow/arrow_flight.lib,sha256=WAA3KIzIh510ieGNs0lfYlX_E04uH6d1nYO62EPZwyA,502618
pyarrow/arrow_python.dll,sha256=sukmttgAYscx7pPnDr8nKcg6F5EwcVNmsry77NiEjiU,1215488
pyarrow/arrow_python.lib,sha256=vQpP3VBejq5JNGJu4Ps2WKq8uoJnf0Qtn5jTlCV_hyI,168058
pyarrow/arrow_python_flight.dll,sha256=4H5EY62svmOV1nZ-e_quUH3NFpdPjK064HUrnTqwOXE,62976
pyarrow/arrow_python_flight.lib,sha256=GOHpwdJ4mFbjJ1eS1Wr4npRzbsBEFqEgkepvRkYK0V0,58204
pyarrow/arrow_python_parquet_encryption.dll,sha256=wdENTWuN16dP4J611BDEUjpJqwQvMzl4vWMPm2hX-Pg,32256
pyarrow/arrow_python_parquet_encryption.lib,sha256=fROY3ZCzOx-YFfc8rI5qaLXrAjymDTyk1FEVwWrzBvQ,18398
pyarrow/arrow_substrait.dll,sha256=MS0De942Smx5yP4MCBwOxLarLWHJBCaop2v6cG-U-Kg,2369536
pyarrow/arrow_substrait.lib,sha256=mUaJYvyHprd_0q0WGL-Y-9RTqZ91TOMVMrHUIwrCfSw,111500
pyarrow/benchmark.pxi,sha256=mrrnfpGQcc4Dxw7EOo7vqzxW8NBcQ3DmPIAvp76U4aM,889
pyarrow/benchmark.py,sha256=i-yvjV5wmnxC-IMo1xAjys6mmM2ea1yyys6m7FzJt6g,877
pyarrow/builder.pxi,sha256=ZXVjiDsbO_Vw_25kUN43AuE0xj7Chw2Ypkrv45eNj2Y,4784
pyarrow/cffi.py,sha256=xRVBRPsxbd8yd5jE4dk4r_oCUonAei8tLBkUbKP-cCk,2477
pyarrow/compat.pxi,sha256=GGSfRvXAMyzcIu6jr6Gt_a7-a0kied3Sn9BwMY8quGY,1991
pyarrow/compute.py,sha256=gDpYtEiHjnRuERga3qYUJSZnKdA_owlfcwHkqE6fiZc,25092
pyarrow/config.pxi,sha256=XrfYecOH_SEfZ6GfKF12G6xcpzZ5DGoDplyRF-8G2J8,3187
pyarrow/conftest.py,sha256=N5mH1HHuRukhykCcsBowvUIged-qLsqrJkyQdmTvZ4c,10277
pyarrow/csv.py,sha256=YdN28bUXVQVe03NbWzJxT6P_mf43pv0pbv463MJQ-Wc,996
pyarrow/cuda.py,sha256=NJdOLaIiUFOnTIeRCaxNttBJDqwe-hbSdww5ls5xSJM,1112
pyarrow/dataset.py,sha256=F6GPXOIn6ND1ayBg0v80LFCpXppPzBEMmxv-OJSXhIg,41546
pyarrow/device.pxi,sha256=nwhrqDer0H4eKTZd6n6LqR2hR5orwhtXMH4-eCIw4HE,5730
pyarrow/error.pxi,sha256=Bl9lR06XhW7mbCz99INvc4Hrc14jlXsGbE4foHPmpqU,9183
pyarrow/feather.py,sha256=tv28WqFPxjswO1Rc-IeoEolm9M9v7GSW7oetghlyQzw,10222
pyarrow/flight.py,sha256=MLoZiSHzbRBjS7CkR_Na-D8LIBBUR9sOQyx8fDr41zg,2246
pyarrow/fs.py,sha256=l-ckv8_jYrGJRrhjxY2G-YAv_oagxgR56jjnOpG98MM,15393
pyarrow/gandiva.pyx,sha256=sWyaFGaXlkISU628qnT4rb0xMJiYdXZcKv37bmyBshw,25036
pyarrow/include/arrow/acero/accumulation_queue.h,sha256=GGQKzB7AAWvOCn4bpcTZYhTcjRSZbQ5jxN5Zx_bEtXU,6147
pyarrow/include/arrow/acero/aggregate_node.h,sha256=rVzdpk8AthruUdmAffAOUNwbCk-TkIp5ccTP1OheTu8,2259
pyarrow/include/arrow/acero/api.h,sha256=ja6ScTS8T_skQGjBaYvgWPX1MBq1brRlYfghWIBsTTk,1183
pyarrow/include/arrow/acero/asof_join_node.h,sha256=hxPcEiCNC1Ssch8dWu3OmpAmOCMCjWd3wp8VGd7qsoc,1531
pyarrow/include/arrow/acero/backpressure_handler.h,sha256=sv-RN2oUGieDG6c9CP_cMVFcq_SYZae7Cce7ksGeQ10,2884
pyarrow/include/arrow/acero/benchmark_util.h,sha256=UE4Xchd9Euwsj5IWtNGYPRzUk4__ld_46yQdaKy8jFw,1991
pyarrow/include/arrow/acero/bloom_filter.h,sha256=AI0q2YmV9rzvRSGVbCQ5P5ZGMBA-XSrRGeTFCaKd840,12301
pyarrow/include/arrow/acero/exec_plan.h,sha256=icuTf4Vj8rbQvs5XcymSGiEkOsDkvoey_8Zs_HQG--Y,36728
pyarrow/include/arrow/acero/hash_join.h,sha256=1IF3MC8L9U3xtjCS2tCp5ZHANY8hYUcAcKBA02CiEdc,3097
pyarrow/include/arrow/acero/hash_join_dict.h,sha256=yO6tUuaMiNigMvIcecnaaCm1m0vO4RnF3AW3INGEvko,15678
pyarrow/include/arrow/acero/hash_join_node.h,sha256=dxmanwGxru6Ilk4dGouZ4EZy5G8MYpiR3P_Xi1rJorg,4481
pyarrow/include/arrow/acero/map_node.h,sha256=g7SnGKKpDrDxJAeLVRE2dYiu1ASWWfKAsXOovFURDRc,2709
pyarrow/include/arrow/acero/options.h,sha256=YV5y09sfUN-TQfcoVInhD35JClK086eji1cc5CbYZEQ,38263
pyarrow/include/arrow/acero/order_by_impl.h,sha256=Cel0AcoqvQxRHbVFU0nOoqN_omfw_BsD5sQVlo7m7fQ,1747
pyarrow/include/arrow/acero/partition_util.h,sha256=P11DsqUN7wMhIyhWD8JTRpoiqpGJyToeiuo6EpueZRA,7623
pyarrow/include/arrow/acero/query_context.h,sha256=AK7weKm9V7oC5VMvMoAfD6QbYUH3Gwc4QYw5pext4PA,6363
pyarrow/include/arrow/acero/schema_util.h,sha256=krGNXK-XUVhlYvAsLARhGaT5_P3b6jaWt9DF3hiwtMk,8187
pyarrow/include/arrow/acero/task_util.h,sha256=PCgJOqWIHvFm4xEw0KhSb49syeIGlyC3npyFQGfzF7E,3808
pyarrow/include/arrow/acero/test_nodes.h,sha256=wjrgNEl6tIG207vxFVqIO_HbZ5gmwsViKS4az88BhuM,2963
pyarrow/include/arrow/acero/time_series_util.h,sha256=zRkyQY6_JoXV3715mHGxs2c9a9BM8rDTMbBLF6qcCGc,1241
pyarrow/include/arrow/acero/tpch_node.h,sha256=8RTrew_dsrCb5P0ltZ6CRInrjb9R8GqfH_xlI5e83Vc,2736
pyarrow/include/arrow/acero/type_fwd.h,sha256=ywhZdjTAngHGlVcg9SquUKtC-8pZbZaGPQ-vfnbTaXM,1139
pyarrow/include/arrow/acero/util.h,sha256=ZF5uRHwkNWg96zRvue9McAEoNJp_Kp7SMUAeX4oGKPI,6305
pyarrow/include/arrow/acero/visibility.h,sha256=AkfHK0MhXhWEHUuNn2E7LkfTSLVQ3OsQWEzZL67tB-g,1666
pyarrow/include/arrow/adapters/orc/adapter.h,sha256=B7QodNYkYUns8IZhLsygyLlcIDOm47HNRyqAqUf5Qac,11354
pyarrow/include/arrow/adapters/orc/options.h,sha256=vMxdAhBsxRoLUhi1KduGh0B_Hy9l-JmNZI4QQKNJ840,3816
pyarrow/include/arrow/adapters/tensorflow/convert.h,sha256=PBuoNq4ASqWeVqi9cjrTTBc_MOn_FrFUH2uosRg8PIk,3593
pyarrow/include/arrow/api.h,sha256=HajNiML4B--LKCoUN1SX4V_J8nwoFtf7rWcI4YndkM8,2538
pyarrow/include/arrow/array.h,sha256=cAYOBSZOk_RffGZsl994n2Z1utf0U6Xr25K_UbQ24Y4,2030
pyarrow/include/arrow/array/array_base.h,sha256=hduVKHA9x9Xnrh1KmaX6yfEGBh1vZy-Z4WYsjZC6wg8,12694
pyarrow/include/arrow/array/array_binary.h,sha256=cFO7LYPTXKxCBHxylwQJx35PSGzxGyMr8my6fvX8HrI,11568
pyarrow/include/arrow/array/array_decimal.h,sha256=1pr51z_v1LeUPynfDexkpFQfD5MfcONS9J99DJRPpQw,3209
pyarrow/include/arrow/array/array_dict.h,sha256=_rafDEnCRQsrbAA5TncNdF7qp0z0JOn0bHqaB_2yQwQ,7793
pyarrow/include/arrow/array/array_nested.h,sha256=IdCDq-0tn0SKsd1dARhjx5ex-XqT0SXCZDBdn09GR10,38492
pyarrow/include/arrow/array/array_primitive.h,sha256=tVo2Nkaw7Rgxn9-ZGL1DG582VSjoI6Qnm01FtPTD_6I,8404
pyarrow/include/arrow/array/array_run_end.h,sha256=9ZsbKbROro2Cj3e-QXJyjoDscjbDa15A3eJKXVxT_VY,5234
pyarrow/include/arrow/array/builder_adaptive.h,sha256=z6xhvrZdME7Uc3Yw092xhiSOnA2RGq_vJQXCRtNOq_0,7076
pyarrow/include/arrow/array/builder_base.h,sha256=RQahyZ-iZjrz_UsCu_Ub0ekIe-WHMZhZ6ZuqlCS_FYw,14094
pyarrow/include/arrow/array/builder_binary.h,sha256=qFMECfoQian0X7LcdtVaw3p_BpoWruH54z9mFv9Geoo,34737
pyarrow/include/arrow/array/builder_decimal.h,sha256=6NHMQaRvqtCSuYoeyVFkpWW1DMaWKkM3cBHePossbJQ,5215
pyarrow/include/arrow/array/builder_dict.h,sha256=nF4JDCER_0_7UaFr0fxe1zVFsGNFhtbJ4sPZ7xEEfiI,28627
pyarrow/include/arrow/array/builder_nested.h,sha256=FK7ZrhNMq482AshcJG9g7CQDjllI5yCW_jBiyn9YoUM,32067
pyarrow/include/arrow/array/builder_primitive.h,sha256=bffKET0A1_iOlcqY1XkexNRkoVp3VmEI1DLsC_ortys,22597
pyarrow/include/arrow/array/builder_run_end.h,sha256=XLX6GrEjnYcVul_Tp_c3NGProE0OpogU-xeI55nhtlk,11719
pyarrow/include/arrow/array/builder_time.h,sha256=L7-Kb-5xOUYkBp98Y7vBMCPyQba5qXb5oChSgMtvaSM,2614
pyarrow/include/arrow/array/builder_union.h,sha256=QXRVqMhp3oHs-qU6xl0y1-KjrYq2LdG2XbbzGN3tQMM,10398
pyarrow/include/arrow/array/concatenate.h,sha256=JhkBRuB5TM6KbU4NQ_flPeVHDTR5Kg0C77XBz3uB9qM,2112
pyarrow/include/arrow/array/data.h,sha256=jGJcEOSrAxIVyRWYx92MPAKZIsTl77FYLy8MFQ5pxAo,29693
pyarrow/include/arrow/array/diff.h,sha256=vGHW50FpxoMfYMVRvzHcxx3tw-02letyoXkR5yoxgr4,3420
pyarrow/include/arrow/array/statistics.h,sha256=TpjlnQ-4NdE-5hhF7UI0ePshtKSsVDQG6dYplbMmYYI,5784
pyarrow/include/arrow/array/util.h,sha256=T48nkLcn4eUkvFWe40eIIBG4ZiB221snra0gACpEIjo,3748
pyarrow/include/arrow/array/validate.h,sha256=9O3KehNl2kWHQwLz6A7izxqW2McmpVcmOE2nz1glSBY,1766
pyarrow/include/arrow/buffer.h,sha256=V6GgttIC4VursfJ4BzpuGs5DMASkv2qK5a0Lzn3vjb4,23813
pyarrow/include/arrow/buffer_builder.h,sha256=osWUkr-zZ0d0rN1fRNwOgiT-8BGDe4bzZs6d2TQk8CA,17963
pyarrow/include/arrow/builder.h,sha256=MNcxVdcYID5LCq_U59nwuObUVshQiEB3_5ij65s8JVI,1579
pyarrow/include/arrow/c/abi.h,sha256=WzE98mONnJJzA9pTj7g9rrdjMNBOznmRf1arZJYuHHw,20778
pyarrow/include/arrow/c/bridge.h,sha256=F0wa9Z9e3-gMdiwvDftaxYT2ao_W3gLGUT623x8RT8E,22278
pyarrow/include/arrow/c/dlpack.h,sha256=-VskMNBoK4EWtWgnQxE2JnvT17XUzadZIztG09IdknU,2040
pyarrow/include/arrow/c/dlpack_abi.h,sha256=NIJjDKuCpgkL3_OoGaJQ4apP5kW0z38Fw2Gw56GR6Jg,10241
pyarrow/include/arrow/c/helpers.h,sha256=K87ufXnopM9P9KLlI2r70-E35F-RnaVXV_bzZWtT0-s,6457
pyarrow/include/arrow/chunk_resolver.h,sha256=9qIdJxsGq4kPALXnSEsZUN7Gx7h2v-drutsG81jnt9Y,13135
pyarrow/include/arrow/chunked_array.h,sha256=4R6KLJvU1WgaJFH2OtwSsJtkbY2TRjGxS75NfEG91nk,10930
pyarrow/include/arrow/compare.h,sha256=ekxs4oEhJ9ZHLX3WZ3CGhix2jSqB9iPn9Zp7kKFfUUY,7073
pyarrow/include/arrow/compute/api.h,sha256=RjDyauneE0xU6zlpj1TFfFJHTCOwNLZVCCccb3Gb6wk,2193
pyarrow/include/arrow/compute/api_aggregate.h,sha256=JCI1OXK6Q3jGGSU7gkIUnjqxAH9RxewkhLl0TixNYHQ,22541
pyarrow/include/arrow/compute/api_scalar.h,sha256=Uhs0jmdR4mCnJ12OKOlzfG4gbJKRrjYPeF4DyxREhP0,71901
pyarrow/include/arrow/compute/api_vector.h,sha256=h5strsT0d3dajRe-nmo9eje3Zinh4diGD1oNf3O0wqM,35342
pyarrow/include/arrow/compute/cast.h,sha256=aCqOeB_N7h2qGChv9yAi9NqUZ_33FptkCoIFCmOieog,4379
pyarrow/include/arrow/compute/exec.h,sha256=GzwemTPvEjJPGjW9GRpvMzUn8jhbWNs7FngwSSKPagI,18464
pyarrow/include/arrow/compute/expression.h,sha256=a4PE1-4ltZTINj9TVH-88zfY9xYcLHQnfPidRoFjIoI,11459
pyarrow/include/arrow/compute/function.h,sha256=L5oWje4X82sATTQrd2HL4Db1-gg6DwH8zOyw0odnYFM,16754
pyarrow/include/arrow/compute/function_options.h,sha256=FnCqSjeZG13AUwDx65pOj8i3ZbHPhzL4hcpyRgpB9Gw,3169
pyarrow/include/arrow/compute/initialize.h,sha256=hoUZTN5eYePB-RDPK5icbvzI7BV9EoGyb2oREuknidA,1225
pyarrow/include/arrow/compute/kernel.h,sha256=ZUFUO0FLpVhMWbLgUpf9bU_OFVaJxyZF-TOS3HHHG3Y,32159
pyarrow/include/arrow/compute/ordering.h,sha256=ja1S3q-ywF2y02-5-jPcG1KVNbx_qLRJ5Q9NIr8mxco,4249
pyarrow/include/arrow/compute/registry.h,sha256=kZ4rf8OIM0F-0wlfO66KMIDctZpDdt9asfCDt-6tRcg,4963
pyarrow/include/arrow/compute/row/grouper.h,sha256=K5eJwGWzDsAOlQHNmOhq81IG8R4ZWo68hhkU3i7Xh8A,7718
pyarrow/include/arrow/compute/type_fwd.h,sha256=uvRR4uzdCp6KMhKrtK6SSlnIeuHakRUt1dgXC8ayzyY,1614
pyarrow/include/arrow/compute/util.h,sha256=oJejQXgrPQINIaqvwz5HxLUFMLMkLVtJKMSvr-eCwe0,9597
pyarrow/include/arrow/compute/visibility.h,sha256=uikdi2lpZVLpcW1vkTFSEma9K-Ej-9ixRRlYwMWuo_Q,1646
pyarrow/include/arrow/config.h,sha256=_-TrLaku5IaDU8KR1K3Y1gmLJKhHv6teKgZHIFC9nEk,3142
pyarrow/include/arrow/csv/api.h,sha256=mrySGIM81ziJhlG3AutB28biUs42PNvpERB63vFfXQM,929
pyarrow/include/arrow/csv/chunker.h,sha256=KYhQi-hLLmFIhC3BRfZRbIOpZMsTbnWHS08jy4tcCl8,1207
pyarrow/include/arrow/csv/column_builder.h,sha256=tvZJ71f-fylYtrEmkWwIxgb_s2WLiUnPwyBpBLCHdjU,2968
pyarrow/include/arrow/csv/column_decoder.h,sha256=1Mfwma0yKt5cYVQ-jb4kaHk6VzrnyrI5Pg397R-Mm3E,2422
pyarrow/include/arrow/csv/converter.h,sha256=eBnvOyrqWZ7bexpZc35jBFouaVFFn0NyaAM0P0chcfQ,2871
pyarrow/include/arrow/csv/invalid_row.h,sha256=QI1O1M00RItEnFeMYTvJCUOQ88n-GyO0IantTDu73T4,1944
pyarrow/include/arrow/csv/options.h,sha256=xmUJXmLQVePc7alEXt1vaWWVEU2HpdDOr1SyzvqrgAk,8200
pyarrow/include/arrow/csv/parser.h,sha256=rT4dfOfzKBFAbe5WQmrWtKbZ_DtFRGN4VaMcHhHVc_Y,8844
pyarrow/include/arrow/csv/reader.h,sha256=njP5lhKW0XptbU_Zz3LakWZdvrg7Eko2N9i0vKEKBcg,4718
pyarrow/include/arrow/csv/test_common.h,sha256=dw0MWN5occOCyrIoIOzppsWMY83Br2FMgTDGcWC-EFk,2027
pyarrow/include/arrow/csv/type_fwd.h,sha256=udWl775X1Yx5QBgfmXluDDmgI2NStrjmur91H8FNvg8,1012
pyarrow/include/arrow/csv/writer.h,sha256=9cBNYlJRIdqJ6RlWhjpbVq0pBPVwgIdkmc4up9Cah3s,3639
pyarrow/include/arrow/dataset/api.h,sha256=zUvEjz-PuLXDdqE_WUVIWAwHAmz77XwqI8eDBqpHAxQ,1361
pyarrow/include/arrow/dataset/dataset.h,sha256=D6fwrRvAofum40w65XwDqaLuChueQqI5ZPE_Sdq3Mo8,20818
pyarrow/include/arrow/dataset/dataset_writer.h,sha256=eq88v7MOtVt44fg54_1bo1vzdz8v4RPJGuLVY_ai1UU,4692
pyarrow/include/arrow/dataset/discovery.h,sha256=W1SwsuLp-H9IzogC7Ew4_DbatXPH0AERyY0uzDVbtaY,11511
pyarrow/include/arrow/dataset/file_base.h,sha256=5DK4suLGB2-NL5DTspF__9pYC-b0z4k8LrRRZToeQFs,20884
pyarrow/include/arrow/dataset/file_csv.h,sha256=MCt6eRH9CKrfmMPmZUiQcJePXAT8ynI4qeLcnhV6pHg,5160
pyarrow/include/arrow/dataset/file_ipc.h,sha256=FaCnXdg2YihUDxcdBoCvSmA-MTk-VKhD-rN5T2WqEkU,4206
pyarrow/include/arrow/dataset/file_json.h,sha256=JH-9JGSg351WTLG6ET6YCQ30w-Kfcd1Uwa09nAQX0LA,3621
pyarrow/include/arrow/dataset/file_orc.h,sha256=i5In5ilw82h0wil4IbSJpFfSU239_6bFsZZAx3e4vQI,2527
pyarrow/include/arrow/dataset/file_parquet.h,sha256=5Ab3rC2NBUF7lK72HkI9FhbSYPRMZD2vfLVVx6BqLBc,17345
pyarrow/include/arrow/dataset/parquet_encryption_config.h,sha256=8wPZEYEs9-95TaL8hwZcsAmmzldvg20lqBFLT4YMwNA,3500
pyarrow/include/arrow/dataset/partition.h,sha256=P9uWiM2sCk8LWEl3vfzxBwDZHdKGtjehzALCctBq3As,17247
pyarrow/include/arrow/dataset/plan.h,sha256=e3HxDV55B9606UzmQfJOchsZYvxNjjSRW1sSeqRdA38,1214
pyarrow/include/arrow/dataset/projector.h,sha256=Gl7qon2MGuQdAXFC_VL7lDmekOoaIYpkDbqvhHaOmtg,1167
pyarrow/include/arrow/dataset/scanner.h,sha256=BLMu-WiJQvpIH_Hn3sc8lWOngk223Jgn2Czo1nl5tlI,26532
pyarrow/include/arrow/dataset/type_fwd.h,sha256=3E2qO5bZAg-AAtFGZGGpWP_jidXMKyxgsZL7KhlquvE,3283
pyarrow/include/arrow/dataset/visibility.h,sha256=hNHTDNAve7L5iGW8cE-ZD8_FwF50tSOkPIXHgnQmWUA,1636
pyarrow/include/arrow/datum.h,sha256=qLmvRILWuUfJCWZ1b1_dXAN_NQRwebjjdbSs7ow2UPk,11825
pyarrow/include/arrow/device.h,sha256=M0wyVZkkbM7G2h4pISWygSJfxUStvsNljOjmQ2ZHIGg,15726
pyarrow/include/arrow/device_allocation_type_set.h,sha256=m3zFNvCKW61HQhaH65_97m-y73Y0mQTOTHTSJFMSpT0,3403
pyarrow/include/arrow/engine/api.h,sha256=fgaw48YHO1VT7cqs5htHz--cpk4Zh9nAsn23ySIMx4I,908
pyarrow/include/arrow/engine/substrait/api.h,sha256=cet1Nuy9YHB_TC87wqBbljw95HcuOABH9cBumBtrhLY,1105
pyarrow/include/arrow/engine/substrait/extension_set.h,sha256=XK9PFvRhyYUCR5aiOjnpDimCDDLeQEd_PNnGLJXW9rs,22033
pyarrow/include/arrow/engine/substrait/extension_types.h,sha256=XuG6iwVFe8MtMGSOyAgL1HnPrHOS3O6Nvcf79Ew4zr8,3165
pyarrow/include/arrow/engine/substrait/options.h,sha256=pYixPsZpfJoc_2DK6ludyaAjZtGsWIh8D4VQ-UEnREY,5955
pyarrow/include/arrow/engine/substrait/relation.h,sha256=YwP9-cQSQXp6YbHN5tuO-KgiL1Pu1GRhUAAlV_9V2mo,2456
pyarrow/include/arrow/engine/substrait/serde.h,sha256=CDi5lFowebB2mlxr8JvfpgNFV2x1fiYiXtrEf5O4QSo,16859
pyarrow/include/arrow/engine/substrait/test_plan_builder.h,sha256=rb4YPCPcFnzYKBX3q0Ar-ceTdtGwFpkOqCK3IsJn4e4,3079
pyarrow/include/arrow/engine/substrait/test_util.h,sha256=TGz1HkUVePC7x29UW1sqc2mP1F49n4kGVdTeLJab3gQ,1562
pyarrow/include/arrow/engine/substrait/type_fwd.h,sha256=aUfGCwouyEBJget9lsYmu6H_Je2AmKG_w0R7n18breI,1060
pyarrow/include/arrow/engine/substrait/util.h,sha256=CacFeBx3Zb_tuS4lL1Oo6Mu2xbH9VpMxEWavuw2HQh8,3653
pyarrow/include/arrow/engine/substrait/visibility.h,sha256=SjFehPWLcF8wifrR5gii-C1_yUr95k43uYONab6nYbs,1792
pyarrow/include/arrow/extension/bool8.h,sha256=kpVXrN0XbSZkz5ttuCdrQ9y7QZuZoSc1OqcaUOoTv90,2205
pyarrow/include/arrow/extension/fixed_shape_tensor.h,sha256=FY5tx6BATFM4w1wAKn93whWsaccrs_qA8sXdXz5oj4E,5740
pyarrow/include/arrow/extension/json.h,sha256=ChlBiqufVFsuHeYdS59TMitoh0aaIC6nVZf8nbxqJRc,2083
pyarrow/include/arrow/extension/opaque.h,sha256=HoNjMGE8dsyen_D1mKIOlyNeHfBXVFkBle9CrK0FxLk,2991
pyarrow/include/arrow/extension/uuid.h,sha256=2EKEGYHwP7I26ktAA27rP_7XaBmUxf5g1sFMONf9OTs,2427
pyarrow/include/arrow/extension_type.h,sha256=5T4teNlY7ikSNCVyZ5DUpLiX5AwD0oAUlv5M6ivWv6I,6807
pyarrow/include/arrow/filesystem/api.h,sha256=wYLtR7Phx5pfHm8SKXnJUqYS-e4ffUAuKsWE8TGvs38,1417
pyarrow/include/arrow/filesystem/azurefs.h,sha256=qGfJiPlTp2EcOSUY5HqrtfE_rzjkSuB9wVuExgYOdBQ,15672
pyarrow/include/arrow/filesystem/filesystem.h,sha256=P7D9OeV2qwDalK-7FpqpC4OsFrvp8YhZ5SCjaMDH920,30327
pyarrow/include/arrow/filesystem/filesystem_library.h,sha256=CiZ55rYBcdvZltIjjbre4f-b8OlSZ0Xf_RMIYRSYgqI,1764
pyarrow/include/arrow/filesystem/gcsfs.h,sha256=dX2z7NpQxXhi_Yal302jFz5jX63hadi2-rjNZOZCDNs,10614
pyarrow/include/arrow/filesystem/hdfs.h,sha256=aewVrtV752VmzKUmbXsV2WidwyFuwm9hFpGQPghUMpg,4250
pyarrow/include/arrow/filesystem/localfs.h,sha256=xRwJh4SNFE5p5ZEHolvOH5YrCZoUWXfk68YpR3gr4Wg,5104
pyarrow/include/arrow/filesystem/mockfs.h,sha256=mPQsmYNbOHYIpWeqnJGsUWROxqagJyxjYaDgHH5NLc0,4902
pyarrow/include/arrow/filesystem/path_util.h,sha256=r7xAj9YOEnkxmgACJTFHjjp21brC2cg5oF4NDZeChy0,5876
pyarrow/include/arrow/filesystem/s3_test_util.h,sha256=fpkDrqz_CkKzBexQxilPbk8Lm39Ri51lLSSZzCp1k1U,3070
pyarrow/include/arrow/filesystem/s3fs.h,sha256=UY1HFTNMuYWPeJ8DENwk2wDYOuVrjVTPOswmtCb5-Og,18688
pyarrow/include/arrow/filesystem/test_util.h,sha256=d9uoQLLFXh-jk_m0to7p3X-FZhHcpKu8FQXJjbcPl9g,12002
pyarrow/include/arrow/filesystem/type_fwd.h,sha256=Wa3zB9amkp6XJARokB7oVDFUadSK6hdsc9wRlswnqms,1515
pyarrow/include/arrow/flight/api.h,sha256=qsYeE0gW9Ja7_AiuRPcSjPG9Za8LhvnIx8xNEgGXJw0,1287
pyarrow/include/arrow/flight/client.h,sha256=jcx-D5BP9W7eEKornZ3gtJfFITpyTcM4TDunG2gt_wk,18228
pyarrow/include/arrow/flight/client_auth.h,sha256=S8w0hGHthx5trhh4Hy2IIUZhlMv-5hadI9vZr61caDw,2278
pyarrow/include/arrow/flight/client_cookie_middleware.h,sha256=1fsK1K4nsZya2Lcu7ObLFmy8MySWAndeOLqbtELH33w,1237
pyarrow/include/arrow/flight/client_middleware.h,sha256=qtGBiIz-xPDGTXqywbFkpnDHI1zMbaGzg5nAFPt3axA,3026
pyarrow/include/arrow/flight/client_tracing_middleware.h,sha256=htiyzC8DGhxUxP4mT2BtC2Z7YbyKIEOPMshLh98W1MA,1251
pyarrow/include/arrow/flight/middleware.h,sha256=D-QPVX66oYEtTqlhvkoKwwXJOkl46CWQ78QHMvNSEio,2329
pyarrow/include/arrow/flight/otel_logging.h,sha256=ON05jVmWvTYfUna9lNklZFvJsA93WZrndPLVWzokXXc,1172
pyarrow/include/arrow/flight/platform.h,sha256=SIcPkVbP-yrFvJaXw0H3FuVnFXZgbDH3KQRNqtvDB_w,1240
pyarrow/include/arrow/flight/server.h,sha256=Uid3mgf9cpE6BoNHq8V2CtbvKvnGFh5DI6nz_0I1o8I,13512
pyarrow/include/arrow/flight/server_auth.h,sha256=U6_gSIgPoMxXjKXnC-InA_nIgtOqQ3fab995KhuOwso,4564
pyarrow/include/arrow/flight/server_middleware.h,sha256=d4Lpsdn7Ge6pump7w8lGixjCM4PXPqUJB5SRgWlwGcs,3237
pyarrow/include/arrow/flight/server_tracing_middleware.h,sha256=4Ovp-oUJe9MT5bM_mlN9MoYLXB_fySwUTy7C6kw9k_Q,2254
pyarrow/include/arrow/flight/test_auth_handlers.h,sha256=OMFj1T511ROS5To6lDdf77UjULqQwGiJD7MIjgPZNfk,3404
pyarrow/include/arrow/flight/test_definitions.h,sha256=Z8t86-GOHwUAljFNS9n9F_F_sgVCaQlVCzmepSSZO3g,13428
pyarrow/include/arrow/flight/test_flight_server.h,sha256=_LRrmCZ7-HqN8jgW2FtSNaAmUVvrhWNJDk84744mUd0,4022
pyarrow/include/arrow/flight/test_util.h,sha256=Txmi3L8DAHH1gsmE_CT-Ebq0s0asNr2WLi5hCdnBY0Y,7350
pyarrow/include/arrow/flight/transport.h,sha256=PeBIjWQuc4oaeHG6OytcrbCS0kKuZYV5PFyr8WUELf8,12479
pyarrow/include/arrow/flight/transport_server.h,sha256=u_0y80TFC5hbZ2TKU9FD7UKbvaXZxf4YbzkyPnBlQRs,5401
pyarrow/include/arrow/flight/type_fwd.h,sha256=1dpmGmV-kYZFcGJSt2_B7yPUHRKeTSgEHz_6sEZaAew,1862
pyarrow/include/arrow/flight/types.h,sha256=Jl0_0Spuf3jPrByZvj3Jv4HJRw_xF27TZOi2hDIJahk,48576
pyarrow/include/arrow/flight/types_async.h,sha256=gTu4fBjhT1vcIBzV8U_4N4I4cpjJjtcvzoDHMKI1Miw,2675
pyarrow/include/arrow/flight/visibility.h,sha256=2qZkjy01imXTXyjlPcuxXbIxYNkb8EYsj8Kr0rXy2Jc,1644
pyarrow/include/arrow/io/api.h,sha256=Dv-t-VQoHXkWlCaBqhV4JNRpbr5VPPSkidR3OuX64fQ,1021
pyarrow/include/arrow/io/buffered.h,sha256=wvHxUeDRx1JuuzsL7iqjKtfi4tSQAob5SYTG7yqXRMc,6080
pyarrow/include/arrow/io/caching.h,sha256=jzGdwSoJa6xM0g2pFC4Cy45k-qP7SU02GAMH8A-eDE0,6865
pyarrow/include/arrow/io/compressed.h,sha256=54vWktZDHbUgxil3pxn40RbcEE9UXCCXmgUOXAO8xrs,3898
pyarrow/include/arrow/io/concurrency.h,sha256=4LtT4aME7nVMW_u7tXY1lHUccm3DCk8n3cjtGkUlvHo,8197
pyarrow/include/arrow/io/file.h,sha256=mud1ILk2zK3C_jap1m0X3SWa2t_fJKi_PIDIYIkWQnI,7846
pyarrow/include/arrow/io/hdfs.h,sha256=_aRZhApwTn9FEUzDGM0eAjt358DIQhxJjHDtIkv3Adc,8843
pyarrow/include/arrow/io/interfaces.h,sha256=knb9p5UmNQlh59Pu0RNf9Vb_-H_STZdxOCCTg3P89uM,13790
pyarrow/include/arrow/io/memory.h,sha256=a3YDNpCFoDqt8eOFIInSznvtLWOH_lR_ePuqzMkKCiM,6518
pyarrow/include/arrow/io/mman.h,sha256=JynPLLGCVYj7QUoYhmpfrDxX5smbx518TosmUpsTWk8,4280
pyarrow/include/arrow/io/slow.h,sha256=E0RbN0tQQRJRIUgM--NznPmI6fLH0bxnWct1jsn370o,4060
pyarrow/include/arrow/io/stdio.h,sha256=FhklhNBsYt0fZZKBmgBfMJ_y_QPcEBkD62KQqedy7Qk,2177
pyarrow/include/arrow/io/test_common.h,sha256=rkn9wORsf-2Ux5-5QReP450OlxVZ5S63mU_mGmNijbc,2215
pyarrow/include/arrow/io/transform.h,sha256=99VKVlOO8M4HF5121bJ5iAyi0eUMqg0ndafvZ6F4y_M,1950
pyarrow/include/arrow/io/type_fwd.h,sha256=ebFvHA60xc-jpcNt7AzadAzwO-LWmFXMx6AMZMgtcIM,2392
pyarrow/include/arrow/ipc/api.h,sha256=r9Wui420OiJIULjh-f019qptrVDtmAr9B-vvVcGly6g,996
pyarrow/include/arrow/ipc/dictionary.h,sha256=RWQcycPKBl5aREIPi90Yd8jPqLcw6ioztlHAHpj1W0c,6281
pyarrow/include/arrow/ipc/feather.h,sha256=LcTc9nldeLieMbtMidRgdvM9X25X4hEaVjZOHAYeofI,5068
pyarrow/include/arrow/ipc/message.h,sha256=sAAyRIrgsRX3L8WMoQ2YqBg_mkBaUazvPjaR3nKYMnE,20576
pyarrow/include/arrow/ipc/options.h,sha256=-rsTvgialw_eHRU50BlHhzOvPw479G9RBi_LPxKMTWY,8101
pyarrow/include/arrow/ipc/reader.h,sha256=mV4_2RaFhhQFUKfrOQtbrYpFGT-qYHKGxiLLIUhipUE,24744
pyarrow/include/arrow/ipc/test_common.h,sha256=P1Ic-77ogbDdn_r7uZezKvHHlgAoDRMeMQ8ekIl8VoY,6757
pyarrow/include/arrow/ipc/type_fwd.h,sha256=0_J9-Ph_UwBFvwOLRi2yxfVp0Ir3IeLY5YV43Rn6cuk,1508
pyarrow/include/arrow/ipc/util.h,sha256=xJ1KaQe_wnYd9zfzVgJlLgGTVQQqxvb0xnZYutiHkfg,1455
pyarrow/include/arrow/ipc/writer.h,sha256=tLFVvlkKzo55PpYELoiEqXGRolGj2rRUknLTQhaTyZM,19345
pyarrow/include/arrow/json/api.h,sha256=QD-9FK6Ad10h03vRS5PvQWBX3jn1TQ2pTBhLv3kSRm8,900
pyarrow/include/arrow/json/chunked_builder.h,sha256=F9WcNFIXy_q9_Uc6-c37RvGgDFVPGo6bhh_OYIyPU68,2433
pyarrow/include/arrow/json/chunker.h,sha256=HPPfHXfhjAj8rtU-RmseUhEqzKtwP64UQdiikMw4jAg,1154
pyarrow/include/arrow/json/converter.h,sha256=0Iwxxsr0ACxdQlpmFujSwVeF43N3RhZ_oSrea9YV8M0,3228
pyarrow/include/arrow/json/from_string.h,sha256=d0k95poMSwvGcm7cyGhM6B1fNKLBAiCCK-tq_4AA5oc,4218
pyarrow/include/arrow/json/object_parser.h,sha256=M-KYzI5UnXpMHVzLhFAcARHmX_E0Py56v7hgVHKfxyQ,1681
pyarrow/include/arrow/json/object_writer.h,sha256=Ad90l1v7OAkkyGiAP2SqTdqyAqG8GmUpz8dxn4bYIdw,1477
pyarrow/include/arrow/json/options.h,sha256=pQRNifTwZ63y6pOBumz6Z8IWHXDUAUQKBPS991Kou7U,2301
pyarrow/include/arrow/json/parser.h,sha256=uLkYKfAOeTDhHgM7FTlSdVxUWne0t39CdgBgHwlZeks,3490
pyarrow/include/arrow/json/rapidjson_defs.h,sha256=xP604P1GxpoA4Mfoc-IliSPEO_eoeCG7bt_ZjwHU44k,1517
pyarrow/include/arrow/json/reader.h,sha256=3RyTYLnpcB9ceSGP8GoKWXqd-ZHZFXto2Fdf0nZ2ul4,5330
pyarrow/include/arrow/json/test_common.h,sha256=5Ejg6ZkZ0MdkX_-UJiKisM7evoNi62SolnDteknG2ys,11204
pyarrow/include/arrow/json/type_fwd.h,sha256=FWkyYf2wb91GygRRLXTTIFvvoztO1hfxNDq8TtivOWs,968
pyarrow/include/arrow/memory_pool.h,sha256=hBekXJc3vipso5c0kVvnsqMi4STKBfX606LV_0j_FnQ,11698
pyarrow/include/arrow/memory_pool_test.h,sha256=tK0L93rXajpMlwHuhd36chOUMMeC4X7p3HiZ2GeXI2s,3461
pyarrow/include/arrow/pretty_print.h,sha256=56IxeFqowZLx9iJHjQgDd74R6obQKSkcy3S5BBxyjB0,5877
pyarrow/include/arrow/python/api.h,sha256=Jmx_-qq-VIvz7SNMf2izoPHXVOMXsP6dmdU_RN57iQE,1208
pyarrow/include/arrow/python/arrow_to_pandas.h,sha256=N-Bl98Z-Y7OrkYzI8pJ5bhR6i-HoCom-Mk1sYCosRAo,5707
pyarrow/include/arrow/python/async.h,sha256=15MshOaSHLSsfKlQw4LCY338khPideC74IpLOFCygDE,2412
pyarrow/include/arrow/python/benchmark.h,sha256=OqCsqRe5osY2lhe2ecN8cXrdkmSFgLp6fXcf4_N9Fik,1228
pyarrow/include/arrow/python/common.h,sha256=VJMEOTBDNX0uY2wFYK-QSpAHoixaufKA7PhbE1uwPY0,14846
pyarrow/include/arrow/python/csv.h,sha256=7yixEpKzdR_wvjfG0rlkmtYpSyjEuNb2cPLueyKFa0s,1439
pyarrow/include/arrow/python/datetime.h,sha256=rPOB2fYu3E7_Lb11RdXglv1tShjA_k52cyz4zw481Q8,8162
pyarrow/include/arrow/python/decimal.h,sha256=cBQOd-yW1ArcUf29lPgEgBUI6I3cCcWT4B6uwd7fTbw,6524
pyarrow/include/arrow/python/extension_type.h,sha256=ovyhXsE-iornOx99_iOmQjZbdqIIpzUfiT7dTvVjQro,3266
pyarrow/include/arrow/python/filesystem.h,sha256=bnXcXSssY90VwD784m3CqQuNnZdwZxKpc8MltD4zJyY,5256
pyarrow/include/arrow/python/flight.h,sha256=dJ6L40ZbpUVKbyc-DytLQFaiUC9rotWFi3ofA5oX3LQ,14802
pyarrow/include/arrow/python/gdb.h,sha256=VN8aDtc1iFPIKsjFQ29RKQOkClvKAAI_KeTy_LFZcZg,1001
pyarrow/include/arrow/python/helpers.h,sha256=VTge6_S23-R8gKOvF8R_M11_FX-61POZMrAxGmHlvEw,5639
pyarrow/include/arrow/python/inference.h,sha256=_44xuq5q-qktVBIigUbwmlgzOMJ4vHuauXQu-pXG1bg,2102
pyarrow/include/arrow/python/io.h,sha256=byOtFjW3NuetpJIErXFG62bsim0ZsWIAPgbes_JvI3k,3979
pyarrow/include/arrow/python/ipc.h,sha256=lHD6Yl4f_ARUIK6SlA4Jy75i7_K940NIGg6vHauLypc,2331
pyarrow/include/arrow/python/iterators.h,sha256=9aFm6h4iTZArpr0jSCx8PNIpzmPhmHgZENio6Ykyx6w,7527
pyarrow/include/arrow/python/lib.h,sha256=aWAOSlSScduvc3gGO6aSAWcNBl2QECWEdQAZb6mIl_U,4631
pyarrow/include/arrow/python/lib_api.h,sha256=4oiKWmdhyjn5OxDSBamrukHpWwQbUioDR7X82dhgdXs,19487
pyarrow/include/arrow/python/numpy_convert.h,sha256=JVUlnUuzWE4OwsoHPl4hvzi5_nkDjHR_wO5Iu8ZEVkU,4992
pyarrow/include/arrow/python/numpy_init.h,sha256=e4zEoclXjOzRydJi6iEa0SQZoBL2xax_odZ2JApkKlk,1026
pyarrow/include/arrow/python/numpy_interop.h,sha256=NMBHwZjVHiOGujS-223OxV9V5l0niyHX3-Qw1MZ-4kU,3521
pyarrow/include/arrow/python/numpy_to_arrow.h,sha256=-JhqPEg-tOBKhwRDhurxrAYQ6I-ws4CoghILzj9R-ms,2832
pyarrow/include/arrow/python/parquet_encryption.h,sha256=2d1-5CgmUZOFIXscD4J2nxuOkEhac0opO0PB3Jp2uz4,4993
pyarrow/include/arrow/python/platform.h,sha256=Otng5ebgyLptzmXIjS9o7tLS71emaH1doLGpDjYDxso,1463
pyarrow/include/arrow/python/pyarrow.h,sha256=Pt2advP3jdTBRb5y2TtyDLVBACieOKI2IZblGu6kPMc,2850
pyarrow/include/arrow/python/pyarrow_api.h,sha256=uPrpVqzif4NlQkJvj-tZCIeBLBLSNE7ekGjKlCPCZxw,886
pyarrow/include/arrow/python/pyarrow_lib.h,sha256=D1QHgdorjjbTko9iMJLaFwykjnjIRBDLyuQaskohSmw,882
pyarrow/include/arrow/python/python_test.h,sha256=kdEUk3TAKggBdER-tT8y-fIcO2UnfEh2K8XFrnDYLYk,1237
pyarrow/include/arrow/python/python_to_arrow.h,sha256=r6iWOjXDn6gO6Qxo9mAlnm6f94jdAE8EmW0D23UJgac,2601
pyarrow/include/arrow/python/type_traits.h,sha256=UPe0QkAuIDKJAgTNmMxIehMePjnl7_rmsJEk6mCONKs,10589
pyarrow/include/arrow/python/udf.h,sha256=gLQGE1vYHlYJ_ceN1Kb0upw6cP90juYZ4b30VvuUPPM,3185
pyarrow/include/arrow/python/util.h,sha256=rUm_q03_LQ7r21r5GDrr1iG7pPzU7X1k9yxkF_IIiAY,1772
pyarrow/include/arrow/python/vendored/pythoncapi_compat.h,sha256=5TMOeKnV-Fpx4R1WeDeJz-3EvvLWyrgGZyCLNMTVVqs,42419
pyarrow/include/arrow/python/visibility.h,sha256=yIEoKC8dabjDOxjUdLB6CiP82nhFkyGdudhLyJNgmvU,1420
pyarrow/include/arrow/record_batch.h,sha256=3R5AjvpbgAKoTI0xB86OTfq_fc7_9lkl53DdSxJ8qGk,18988
pyarrow/include/arrow/result.h,sha256=Wgt1ttTPuUv6yobhRxYvmrPKPswGH73Jv1RTbixvJyw,18665
pyarrow/include/arrow/scalar.h,sha256=T32PFngSH6tvwT6fV8TsBVg5C9UNmvfpj6GRb8zVTKo,37550
pyarrow/include/arrow/sparse_tensor.h,sha256=wlyVEvtE_a0ERgsR7OHRt2VqOP2Y6e-tbe2nkqbx1p0,25822
pyarrow/include/arrow/status.h,sha256=W4Ipqw9YhvmoaDY6dkSruijJQIwgqDKfSwa9nK4a34Y,17901
pyarrow/include/arrow/stl.h,sha256=-lOivcsHPpTRyoxhilxgQKMcnGJE2JWUr60WnWlOkfs,19834
pyarrow/include/arrow/stl_allocator.h,sha256=L7G6TMjgleD13a4fl1cC9VyXUDQ-2fkXe54gf26PfIU,5120
pyarrow/include/arrow/stl_iterator.h,sha256=QDUTi5L6m5hBuvbcRR0PdjkQVFKyiZT5m5qcmAdYN4I,10257
pyarrow/include/arrow/table.h,sha256=oTbfuDHsnyn8KH0bCOvK7hOgA4BWo1YsWHUisI2mVs0,15055
pyarrow/include/arrow/table_builder.h,sha256=kTA3F-72RoNR_YZNmxQmatxC2cUW5feh-cvJEmi0ZQ8,3870
pyarrow/include/arrow/tensor.h,sha256=cxlGYyeQ3q7lKgFtSj0q644T2bQd6HN8D31CHIUsW0Q,9339
pyarrow/include/arrow/tensor/converter.h,sha256=BTiYEWpV34uKMP1hgVxdoeyBlh-cqpmVaaBeIc2ARig,2958
pyarrow/include/arrow/testing/async_test_util.h,sha256=miNSGn91JvXE1NojQusPWGTINiJkk5D5xurkPNwyBWk,2341
pyarrow/include/arrow/testing/builder.h,sha256=MV0S4sm51CGu5c4HA-90PeZlm-HrVfpJJpSuSZhMxto,8787
pyarrow/include/arrow/testing/executor_util.h,sha256=3WdGxQ-F5NfkJVCR0jv7hYG5k0YtdXuOoxACY3bmY7Y,1940
pyarrow/include/arrow/testing/extension_type.h,sha256=darWPV8ch1l4bPv70tV9L5btHpJ1GoVmjX3DXmdPeLo,8363
pyarrow/include/arrow/testing/fixed_width_test_util.h,sha256=bcBgIZw5MhP7JeqHA39Xqu6FBJPoqdKs-89I-bQAocc,3167
pyarrow/include/arrow/testing/future_util.h,sha256=CyeXP1q0nDBKhFD2JpzHFOKhWhLa7ItvQ6OdQgm82Zg,6388
pyarrow/include/arrow/testing/generator.h,sha256=s8d4bIYKCPPpUlFfnlQUTCDWjkvcvoSN9AInFaNFz5Y,13894
pyarrow/include/arrow/testing/gtest_compat.h,sha256=Dpi16YFwo1rIcDWJp82L8S3uSQGBz7cfrAp-mA3rYWA,1344
pyarrow/include/arrow/testing/gtest_util.h,sha256=HZ6LgxBH-10dJUFj8P_efvp85YJLVOBeMLs9OpOHfMQ,24956
pyarrow/include/arrow/testing/matchers.h,sha256=3gN-JYYEcXZCUYL9fvTTEAOOBqt0qBlrb-jtinmgDn0,17299
pyarrow/include/arrow/testing/math.h,sha256=j15w8jqrSSYFiQQ1Y7T10ar4QnS05CpHvZ0779oOh3I,1244
pyarrow/include/arrow/testing/process.h,sha256=gkmAniHmyI_3uO42ECd4vE05YwqkncQCrJ2RqlAe9c8,1418
pyarrow/include/arrow/testing/random.h,sha256=xLgOiCb787tAQPnQdP-zDsBudH2YdFLUHDHbVKrpus4,37780
pyarrow/include/arrow/testing/uniform_real.h,sha256=8Us2dKOV8LhsB-MawOoCBPc7eLzP35KKWVemK0UZoRE,3054
pyarrow/include/arrow/testing/util.h,sha256=s-3vR4XpJLxkSKel3H77QT0uibaj6X7LmZMGTOuO0kg,5726
pyarrow/include/arrow/testing/visibility.h,sha256=gA2cVeS6ElGHHJpuCq1o8GU3iXUnTC62gOyPihyqWwc,1654
pyarrow/include/arrow/type.h,sha256=-kH1PtkipKXccWhKkEkC_552t_qFtDql9rKzYChGP-8,99590
pyarrow/include/arrow/type_fwd.h,sha256=dFIsZsPth44yNyPzSdpToFaIhU8WRvHBPl3KRvgqBr4,24290
pyarrow/include/arrow/type_traits.h,sha256=v4Txumh05ii8AKH8Cm8jsx16CP7Hl8Nq93-unD6vvDw,57769
pyarrow/include/arrow/util/algorithm.h,sha256=okcbieN_LA2UJ29QIEQ8LcF9EblZvyu4iO9r_QK8deY,1262
pyarrow/include/arrow/util/align_util.h,sha256=0ZSxphiZMkkYipVQ8940h8jjB-0B7oKbrGGLzltPZCw,10890
pyarrow/include/arrow/util/aligned_storage.h,sha256=fvm2ZK9BN0xyJ2XCuHRDeRA5bInQqPUbCvo9yUbSZj4,4380
pyarrow/include/arrow/util/async_generator.h,sha256=ecTpivooXwNBX9BSW68CJ4OBNr7WMi8yhbD6mg5iHsE,80271
pyarrow/include/arrow/util/async_generator_fwd.h,sha256=aVD2KiSnuZ06Jj2XBmk_cKH2umbhyJTKoDrHDmGRtyI,1797
pyarrow/include/arrow/util/async_util.h,sha256=FvGoRiKle-dv8lJrVYHr172NFJ8X-c49dGZM4YDN2oY,20219
pyarrow/include/arrow/util/base64.h,sha256=ihRHtpYtb8xH71Zu2goxkm7LKIAnQkzlsSgSUQu7BlE,1130
pyarrow/include/arrow/util/basic_decimal.h,sha256=NUYG6vfMdi27mP9zBmYQPKB2UrRpqjJFoCIe_Y_HBcQ,34456
pyarrow/include/arrow/util/benchmark_util.h,sha256=M2D-6zHAMywExxYOVZu-HskXP2tP47z8QD8OPMI3tEM,7852
pyarrow/include/arrow/util/binary_view_util.h,sha256=3zHexr0yHzt-_FG8YZ5wvH1X59M53aiQu42Mzq2k7Yo,4740
pyarrow/include/arrow/util/bit_block_counter.h,sha256=HR4DVUkayHHvzTQbBjAM6TCwSWJzFu8UJaE1YAffvqk,20732
pyarrow/include/arrow/util/bit_run_reader.h,sha256=IhrgAhUy8PBc-Yu85EVLEGB47cQL36NoJe3CIIgfp9I,17731
pyarrow/include/arrow/util/bit_util.h,sha256=k1asaD_FfjD1vh3K5wHbkOjZWgXJp-erJur65drI2Yg,12477
pyarrow/include/arrow/util/bitmap.h,sha256=5Hc_dpy2GPGPkLSvMqs_xmqkM4Ksb4FqZ10Db3CH_DA,17925
pyarrow/include/arrow/util/bitmap_builders.h,sha256=QzrAj7bLmAixE0ry81nA2SJWHoqkyl980r49cra74Jw,1640
pyarrow/include/arrow/util/bitmap_generate.h,sha256=LS3946GllKz4xNShS8VwA-v12rVNgvSLCY4nViapFC0,3773
pyarrow/include/arrow/util/bitmap_ops.h,sha256=Hsvv32TrufUn8lGcXShPRZGFmgwa46URagPg1w9fReE,11123
pyarrow/include/arrow/util/bitmap_reader.h,sha256=T8oLs0pEclxX1n-dlkUJY7JEsehtB4j46OmA8giXRmk,8626
pyarrow/include/arrow/util/bitmap_visit.h,sha256=PU07JCo3_618ZWJMxiTIZW6dfETceXgbjfU1YeEfyYE,3558
pyarrow/include/arrow/util/bitmap_writer.h,sha256=iznccf-wENmUl2C4qa5P9tyCUnarQKYqpoGiYcB7DR4,9669
pyarrow/include/arrow/util/byte_size.h,sha256=Z9HxkPG4DAMSJ5bxpglqUP3UP-vnMViRb9KgTrOhh_s,4085
pyarrow/include/arrow/util/cancel.h,sha256=LwAWfES1tiRHZCyYiOqpxSaRVm71_PxhiLOldIUN78Q,3777
pyarrow/include/arrow/util/checked_cast.h,sha256=0XkAxq3kuBxU_pMZ_r6rmgovb5zoFHhq9nZNuWKkgVQ,2137
pyarrow/include/arrow/util/compare.h,sha256=aslVAwRy9uF49L9neBjVmMOVANewLp_GgadkieX7rWs,2044
pyarrow/include/arrow/util/compression.h,sha256=Rs2N_GisDUu13jxt9sbaz5GutlqFKwEqRxxb2ocqXe4,8668
pyarrow/include/arrow/util/concurrent_map.h,sha256=hmRw2luX9OVbYwIyMXc3fEgof4An4owLnWrAsKgXKpM,1843
pyarrow/include/arrow/util/config.h,sha256=7X7yCOm50kIDCJf8ltTIcbdnDf5LdRZ3MXZlAwAfXkc,2351
pyarrow/include/arrow/util/converter.h,sha256=9Wl7M4FSGKwj_jZnVDELWwkkNcd8rJmlsAeOwZF3W1Q,15048
pyarrow/include/arrow/util/cpu_info.h,sha256=Cg7Alr-NR4E89JZO9KlAo5bH60gVUcvpqBOKZ_yUF3Y,4078
pyarrow/include/arrow/util/crc32.h,sha256=mbAGpVjfRlJS1npQaAgDBWJYe6WeflXM6J-ZBO9MSTQ,1373
pyarrow/include/arrow/util/debug.h,sha256=Y8UN-qs-hijFAWr-LKoNq1dpI2ol-krDuy1xTCVYgb4,1000
pyarrow/include/arrow/util/decimal.h,sha256=pA73IUxU11ybaX0ktxHTGukdwRrnZnbIjUIP2qU3DiY,20814
pyarrow/include/arrow/util/delimiting.h,sha256=ZFc1v-DOeBEBfWYcN4-cnxvQMToxeGJeJZ63Z0nrWgc,7498
pyarrow/include/arrow/util/endian.h,sha256=a_lyNIsgQWjMrTn3YyhCpwR17UepUKh9xzb0QTZGdRs,8436
pyarrow/include/arrow/util/float16.h,sha256=swsjuc9S6j9aDc9-WxZbikxawQSh_utjwpmSwS0_2SE,7608
pyarrow/include/arrow/util/formatting.h,sha256=45DQ8mWxEJy7JAlgeYAMVY3zVi1UeD_y5ZTZMtpHkkM,23183
pyarrow/include/arrow/util/functional.h,sha256=vVBnLo4584uiCYaRQSA-2BzzsNSsyjWbEdgsF6L0a8c,5772
pyarrow/include/arrow/util/future.h,sha256=kWHhE5nOrI6qt0rJJCFQdVOP8yCf-Px7oo4TSEKYmRg,33178
pyarrow/include/arrow/util/hash_util.h,sha256=pUOiWTK7x1Gz0zzhTmB7wQDOaUo5R-J3qHt49z7rEx8,1984
pyarrow/include/arrow/util/hashing.h,sha256=dPLfFC0gC8JaczaHySKsBmOcpSpa8LT_GmRXFwfB3xg,35208
pyarrow/include/arrow/util/int_util.h,sha256=1l6c85GqyP_k5ZOsOyqu0mgaA9YBo_JJe4fv_4eNEJA,4996
pyarrow/include/arrow/util/int_util_overflow.h,sha256=2z44sPGDtAVXvWgVOI85H70cKo_FlUeue-c91aeIfWs,5013
pyarrow/include/arrow/util/io_util.h,sha256=ThGNEUjr7X-jkqbogiRgu57m8FHNvwsLEOjH3RmYuH8,14161
pyarrow/include/arrow/util/iterator.h,sha256=pjJDlXQFWI9F2MJlmitB72T_2xlfMaWHa9SISm62Lj8,18927
pyarrow/include/arrow/util/key_value_metadata.h,sha256=m-o5pRI-eyo_32FVsr4byAdE2zcUKul9ROsqE2Oj9Ug,3689
pyarrow/include/arrow/util/launder.h,sha256=SSTWzD4H-Oy0TExl13n4sWqHiRLlKtBxT22r7JZZ7ZI,1081
pyarrow/include/arrow/util/list_util.h,sha256=qO0VnbXcxWfgMuZ4IOYsQk1Jo83EQI64MP1uK-gyWgs,2083
pyarrow/include/arrow/util/logger.h,sha256=FeQZRydUwfQWgEhMZAQpAPk2d4xXw6e4Wo6lZmqYH6w,6879
pyarrow/include/arrow/util/logging.h,sha256=8sm0vrwf1sBwVVKp8gZ1IPeLSqMKOwnSZaROf7iyEGU,9390
pyarrow/include/arrow/util/macros.h,sha256=6ZUfF1P69Wsp_C58oe4IWiFjB8nMNbFRr3uwMHgibZU,9575
pyarrow/include/arrow/util/math_constants.h,sha256=V4WHS9hLZcdYq2Z3bb4jFiaFSpCUuXqPhF_RooxA0ow,1144
pyarrow/include/arrow/util/mutex.h,sha256=jANHLdRtEDUTIoCYa7kBFRr2fTQlhkvabm1xCKkrFSY,2639
pyarrow/include/arrow/util/parallel.h,sha256=PkkDh5qughPrt47wrO4URgEsxYS-ovMN1fI-Ue6gmVI,3921
pyarrow/include/arrow/util/pcg_random.h,sha256=Wb4huSnThKxOkUJTPjIUZBebkjs-FlzDVbjZjK_bTzc,1285
pyarrow/include/arrow/util/prefetch.h,sha256=2clukiLMMeCJ7R9QNeoogx8TYDUtta_qla-LvkKSJMw,1282
pyarrow/include/arrow/util/queue.h,sha256=vs-xE_jGsLwiWsCiSIshMMPxm-XKxThG6fRLoghFEYQ,1046
pyarrow/include/arrow/util/range.h,sha256=lx2Pwl1BaYNQSJg4VI2ej4JkCRV2ecYDt8W3ac-4AUA,8784
pyarrow/include/arrow/util/ree_util.h,sha256=7h_Ep2HRByiedk0hhdfPOkYP-RhzlhIRgnA-RKTqqkg,22981
pyarrow/include/arrow/util/regex.h,sha256=9Ilm_WKJawvl0MbRXYFU6nCHqiAnctxjEXZDgTK99FQ,1793
pyarrow/include/arrow/util/rows_to_batches.h,sha256=5foVd7Rxoqf7-g5U_yiMyRcVb4zJEgBNrXqpqsoCtXk,7283
pyarrow/include/arrow/util/secure_string.h,sha256=_v2x0J7xaZQBxuxPQ9hTuqKCg_dybA9vEIQiHzwZKVs,2557
pyarrow/include/arrow/util/simd.h,sha256=6Use6liJ1LrtDRAYXoh3fJhOe_O9XU13KupRQ3FKPOo,1730
pyarrow/include/arrow/util/small_vector.h,sha256=aP9xti38tT1JxLqyyTfa1iE-K8UUFitVkz9GEnOtPJk,14932
pyarrow/include/arrow/util/span.h,sha256=hHP8_YM_-35H7ebmm4ndwOegRE-H8w2XgvmfjMbFYHQ,4430
pyarrow/include/arrow/util/string.h,sha256=Qr6Uw8w0R5QTVllapJlhn4xdd8mYk3b7cqGdmVnLlUk,5929
pyarrow/include/arrow/util/string_util.h,sha256=4Iwx3GkYvW0EsehuUFWKn1kN_Os4DfwbMfwnQIyWfS4,2446
pyarrow/include/arrow/util/task_group.h,sha256=ndcevZLV91JlhUjTy0STS6vgfrnlNIds6PH9CogPbY8,4468
pyarrow/include/arrow/util/test_common.h,sha256=di1zrj8WqO82vFlPxZicFqLgm45YD6npiruZrVGEWxM,2927
pyarrow/include/arrow/util/thread_pool.h,sha256=cb0D3t6SBnB_H6ujjQ0D8xiwKqLZEf88lkaXEPxd8Vw,25046
pyarrow/include/arrow/util/time.h,sha256=Qas0nobSX9zpGDHTVkEy4NiRTZmapE6huDafS4B-b9w,3071
pyarrow/include/arrow/util/tracing.h,sha256=-7AEJ2qsSlJ3IMwQ4Gw_5KV0R1UPAmF-YHfFJchO4po,1331
pyarrow/include/arrow/util/type_fwd.h,sha256=JPBUVMn_mAAvXFDrr_k1aYvZD4LBaLP3PryqhLyvd98,1885
pyarrow/include/arrow/util/type_traits.h,sha256=faSyOLMc82vxESSUS9Lx65RrqVVuEQQGVdwFuhNCuvs,2228
pyarrow/include/arrow/util/ubsan.h,sha256=LpyqTqFLkxflIzXjv-5FEd4HvVpoh59TQX_WJ12PR18,2904
pyarrow/include/arrow/util/union_util.h,sha256=Er6Skg48w3vcVZJmBHkWqU9sfeRyyrSO7A9JUU-qc-w,1242
pyarrow/include/arrow/util/unreachable.h,sha256=z23Cp8loRsJJFdIx__AJRBzQzN8dAOnRmktsMIqW9x0,1100
pyarrow/include/arrow/util/uri.h,sha256=7DAZEXo-_N4bLm9dyPlpCge1WgbLRbm35Zvhu_T9Fzs,4005
pyarrow/include/arrow/util/utf8.h,sha256=Bon6sFrfWHTRc-aTxE7AIznWG1UMLsr0iukjvg209lM,2090
pyarrow/include/arrow/util/value_parsing.h,sha256=-XgRaK9szIY4gZWMkJeH7o8YcD0Y3-XTkXd26WreYbM,31045
pyarrow/include/arrow/util/vector.h,sha256=eXtmyXVqBmM4nTrn-2eyPj9uvPs9FFRUcT7ylsCtR2g,5869
pyarrow/include/arrow/util/visibility.h,sha256=_xiFZzfiAuQj8mQJWspWmCq-t-MV_ZUz4NNiEh8D-Mw,2921
pyarrow/include/arrow/util/windows_compatibility.h,sha256=Uz2JwnhEnqXRMLRXmvbCX558Wfv-0zE1MWL7iBzRUa0,1294
pyarrow/include/arrow/util/windows_fixup.h,sha256=0Q0aniGbX6vG0IFVG-1PKUg7LMfOyQ3avtBtgk6qWxA,1487
pyarrow/include/arrow/vendored/ProducerConsumerQueue.h,sha256=kQtEadAfKO3qYjALi1ywgCaE0ov2aGQBUFdMS1qQC48,6318
pyarrow/include/arrow/vendored/datetime.h,sha256=VP3LPk99XyPSS6rNRRIdtY621QBZwEzoJnVNf8Yfihg,1130
pyarrow/include/arrow/vendored/datetime/date.h,sha256=wg6LHOPie1Ul-uhW1aMaaJq56ENAEW99mVPUNTtfG8k,246053
pyarrow/include/arrow/vendored/datetime/ios.h,sha256=Nj5HW986tXE6i_HPt6FScNRhHjn9N4fKCNkBqs8hRMM,1691
pyarrow/include/arrow/vendored/datetime/tz.h,sha256=YZFydjZZWZbGftsIa4gUMinNhGTyINCvCkAfT9V2U-w,88155
pyarrow/include/arrow/vendored/datetime/tz_private.h,sha256=z_VF64I4islcSPxQlUqXz-Xts3IiekINYkyxgFq1XDw,11021
pyarrow/include/arrow/vendored/datetime/visibility.h,sha256=5y9-u5WTzSxSMMn31oyed-qXli8vb5SYORHT-3QDVnA,1032
pyarrow/include/arrow/vendored/double-conversion/bignum-dtoa.h,sha256=ug8vS5I3hPBzzWy8oNbgFxI2TMfB5lH7h2GqA7feh5M,4444
pyarrow/include/arrow/vendored/double-conversion/bignum.h,sha256=2JlGD5vJLfiFtxZPhQ_cxgAp9HslZxsCv75mott_vtI,6103
pyarrow/include/arrow/vendored/double-conversion/cached-powers.h,sha256=0ngMtGe0Z2w-rRx02TXZce4oRwGkUIyqTTXaWDQ3uzc,3145
pyarrow/include/arrow/vendored/double-conversion/diy-fp.h,sha256=wDWYQCswEEMKvIqSrmNV6c8rKuRwEhxjuJfG1D3vMM4,5227
pyarrow/include/arrow/vendored/double-conversion/double-conversion.h,sha256=gxyJNQw6njGyORh7oKiRbeMXOTG7wFhaGLNNf2p4SCE,1838
pyarrow/include/arrow/vendored/double-conversion/double-to-string.h,sha256=LgPPmQstU7ODW1sYBQqPiGqqE9rn9cXXhv4NepiLcMQ,24397
pyarrow/include/arrow/vendored/double-conversion/fast-dtoa.h,sha256=mp-y_ztzGDBZpXrXob3GxoJlVQ2d72HraY3HrBhfySg,4212
pyarrow/include/arrow/vendored/double-conversion/fixed-dtoa.h,sha256=G8xnzmSUSewcLO_31lSDFwGwtYX-UNrPfBnFET9CQBE,2886
pyarrow/include/arrow/vendored/double-conversion/ieee.h,sha256=PkmBx5i6U4mxaVNAHigdSzFSXwy5HnQVToquqqn6wjE,15730
pyarrow/include/arrow/vendored/double-conversion/string-to-double.h,sha256=qVlN6Ipax1SxNPpBLDgwVWiwf9hP03WhQ20V07cHC7U,11146
pyarrow/include/arrow/vendored/double-conversion/strtod.h,sha256=NJ9MHQpDfEtOBOeb75_p6LFmOnkDoe83Lyt7MyR-XxU,3162
pyarrow/include/arrow/vendored/double-conversion/utils.h,sha256=-4-s2NNi2xpcVkQ1FC21SelDLJoDM_O0xPelwq9mevk,16034
pyarrow/include/arrow/vendored/pcg/pcg_extras.hpp,sha256=XtzT6-JHWmGxRuuMnDM9_0AXbOb4lzGWPyTXqdH2mGI,20433
pyarrow/include/arrow/vendored/pcg/pcg_random.hpp,sha256=79Y3YTo5rbOcOJlxfzbA08IrwVXRjQyNwaTw1LSuo3M,75455
pyarrow/include/arrow/vendored/pcg/pcg_uint128.hpp,sha256=pUPqr7_xnyJ7kfoQJPzRHwPAoPEX3XRW5Fou82CCeYE,29419
pyarrow/include/arrow/vendored/portable-snippets/debug-trap.h,sha256=nco_jVnzpnDASOnTnPFE8CCj9uwOWhyV-kBgoBmnf8o,3164
pyarrow/include/arrow/vendored/portable-snippets/safe-math.h,sha256=D8WFFrtN64lU8XdNOdfSKwMMfIHxsqqbbATfuvSA1wg,49239
pyarrow/include/arrow/vendored/strptime.h,sha256=6IG4iT_rLr1Z-r7FcitdiZyEQ28slbb8r8ty5ik57N4,1247
pyarrow/include/arrow/vendored/xxhash.h,sha256=ExWtVPlfaed4_G728m0ZVyo_x78z9lx0E_-aZ_MB0l4,862
pyarrow/include/arrow/vendored/xxhash/xxhash.h,sha256=K0vb7lnmVPp4CV0BbzcTaZgk1Z-QybZFg4dljxt8JBA,259869
pyarrow/include/arrow/visit_array_inline.h,sha256=UtSCAHQQfOIMqAD-Q8gXRqqSOJbDF7hbIcgXx1KHcNE,2510
pyarrow/include/arrow/visit_data_inline.h,sha256=8o6Cq0QwiQBTNAvX7S6BfzwHRkT_Ti65QFJfi1jeUas,12797
pyarrow/include/arrow/visit_scalar_inline.h,sha256=hxfWYgeRl22v7iANSNoIR8RDCJWAYlRRSfhbEIPotoI,2487
pyarrow/include/arrow/visit_type_inline.h,sha256=BpparmGLAnIY17kqWic7UpMNjwjBex3F-zFXRlftzLs,4504
pyarrow/include/arrow/visitor.h,sha256=-EL-W9V3NZIn3sxAEXjF9N-Xae5Gz2O6ScuS6vEzuJA,8881
pyarrow/include/arrow/visitor_generate.h,sha256=MRXQDAazSWim9pqablmsHkHIgqKJfNX1fFUfMNDSU1s,3399
pyarrow/include/parquet/api/io.h,sha256=D0-NRwdb1FrXH8DKZsjZ8MtfZjjUaboR_lv1iFLbr2A,867
pyarrow/include/parquet/api/reader.h,sha256=njU4e2U-9SWf-XtxKdwRqezF-hLF5dAKIWUZFmyawa0,1239
pyarrow/include/parquet/api/schema.h,sha256=rDl6YZ_-iwqcmp7t_rSADxNtiJqJtdl_bHlbOJfCtE0,876
pyarrow/include/parquet/api/writer.h,sha256=wR783UqPiqunq9PY90ae-3lCkuQlScWdFbanzal8NpI,1032
pyarrow/include/parquet/arrow/reader.h,sha256=CqUA-6DYrYLUmOSb316lPC6OUopJEpAWdF-cVmX6WZI,16084
pyarrow/include/parquet/arrow/schema.h,sha256=EBNwRSV2xGo9YQcBQm7lrGrsRoKSy6dkflJlVxxjSKc,6388
pyarrow/include/parquet/arrow/test_util.h,sha256=TqKmN2GuFlVgujpw-l9xyN5L-W3bJtBXyEK9GOk6Hwg,21103
pyarrow/include/parquet/arrow/writer.h,sha256=3wlkytkE4b2Kjj-IykYFc-P1-3p-o1mzm27eAUNaEVA,7181
pyarrow/include/parquet/benchmark_util.h,sha256=8bubjU8G6X38FIRNvymuzKsEWua2vclIXF6BOiDm4gg,1805
pyarrow/include/parquet/bloom_filter.h,sha256=vRqYGhumsQ7pf45UoqmF6IW7v8AMtl66V4SyPsXnNRI,15362
pyarrow/include/parquet/bloom_filter_reader.h,sha256=rYjN1t6FheFqk0_M_uRUw5SVjbZdiF8BDRcjkkXom7Y,2960
pyarrow/include/parquet/column_page.h,sha256=XmbGix_mfx1rstDV-9MsqYscRa0O575Q04xN_415wkE,7121
pyarrow/include/parquet/column_reader.h,sha256=SU75xdlGsEM-lHaGRq-vtqPlC_mlYFWUnncktUmrQGg,19663
pyarrow/include/parquet/column_scanner.h,sha256=kEe6sFLctNQdYHWSe8kZMJn2FFwPpQ9YV-VsHFZYuWo,9127
pyarrow/include/parquet/column_writer.h,sha256=IP_Yj07NKliBl4_H4HhCthdyibEhJFhlSK1ja0C6vGs,12598
pyarrow/include/parquet/encoding.h,sha256=6o1oSv_1HRtMQa9ryGx-kwk2iUz3myxaz2rnjJd9IWE,17330
pyarrow/include/parquet/encryption/crypto_factory.h,sha256=mAJgC8sRq8jT_8vC8aC7gkg9iU3ULMl7t64rQUdxXc8,7216
pyarrow/include/parquet/encryption/encryption.h,sha256=c1gnP-a4oYr09fxp3qXQr1LZot37eOWqXk1YwZVqci8,16150
pyarrow/include/parquet/encryption/file_key_material_store.h,sha256=8HQG0om1FEes8bOWBnRlVh9ZBjOIkRysBzix8-JLp2Q,2257
pyarrow/include/parquet/encryption/file_key_unwrapper.h,sha256=6m9OPhdKPaLQ2dk_-ntCKWf1TzQITrEXLZMW1D0KyJw,4729
pyarrow/include/parquet/encryption/file_key_wrapper.h,sha256=UX5QR7SdyidywA8V8m-zcorleRmXMqFNQmJ8iG0Eg_4,3846
pyarrow/include/parquet/encryption/file_system_key_material_store.h,sha256=LO8-9qDXzGaV-lZqO41dkGSxZK2_whYdsGOARoGhy68,3655
pyarrow/include/parquet/encryption/key_encryption_key.h,sha256=iXagnT2x08O3md_vKPmYzwrZqXdEutkIUGVpO4drTIw,2289
pyarrow/include/parquet/encryption/key_material.h,sha256=FLjsSciNllhH2rfuh6xNGSvwPuNpHE2dTeFeAdy1cEw,6350
pyarrow/include/parquet/encryption/key_metadata.h,sha256=9dLFh5ppO4icbYkJMNpyT1s43wfAY0UIMdgNkzfiWA8,4094
pyarrow/include/parquet/encryption/key_toolkit.h,sha256=7zisONi_O_qn-N9Qc1Ady-X43BJMktV7SHz1Y6h0j-k,4683
pyarrow/include/parquet/encryption/kms_client.h,sha256=0AzqB7Wgr31XPiPihxFGVIYcFo44oz-ydPuZcMMn2SU,3244
pyarrow/include/parquet/encryption/kms_client_factory.h,sha256=OnnMzSQewEckWA1vpsb2g4n7LBQ9zzDueMJi988dV6g,1331
pyarrow/include/parquet/encryption/local_wrap_kms_client.h,sha256=V0XBLFK9V8AeJ0yRvnB17mBF9movGmkItBnVylLPvbY,4048
pyarrow/include/parquet/encryption/test_encryption_util.h,sha256=YjOgyMRAaNDhAsaM5-LFu8PnHiOI1Jx4ODbM7prs_JQ,5342
pyarrow/include/parquet/encryption/test_in_memory_kms.h,sha256=-Whn4W6ydKU0UN6DNf9qmZ-Z6RZX-H4H6H9XefQu_sQ,3615
pyarrow/include/parquet/encryption/two_level_cache_with_expiration.h,sha256=WZoZHBB95ZuFSzcmB5B4N0INZDhlDe-zsNcoENzf0Go,4967
pyarrow/include/parquet/encryption/type_fwd.h,sha256=Y8CtkdgEEkelQiGOHiFxNm0jAtUl84UQS7ucBOK8tmA,983
pyarrow/include/parquet/exception.h,sha256=coKUI_36Z85pAZNtArEtZvf-f0QSpmklQlUUy9dB_7g,5677
pyarrow/include/parquet/file_reader.h,sha256=RpkF2dVk3GWoGd0jTtTkC73hma5nnBUr7QipzzqxrVE,11442
pyarrow/include/parquet/file_writer.h,sha256=iY1zWKySLKxJoDDn2BjOTSE0H8Wj2OSRVzO3nEBpxlw,9588
pyarrow/include/parquet/geospatial/statistics.h,sha256=5sNPeBM_HMnEmTt3uZ9RxLgiomrhU13mY6v8VZYSSmI,9090
pyarrow/include/parquet/hasher.h,sha256=bDhLFfpDeTO3zK6hpb8o3Of_iCx_YooSlRrx2N64gg0,5358
pyarrow/include/parquet/level_comparison.h,sha256=drcd3Q01X1fOv5RezyxondOVPt_vQkc4X003dUzrhig,1344
pyarrow/include/parquet/level_comparison_inc.h,sha256=FxnbEl-Hv2XGSHfOxkdqA4A6s0wBPHt5Q3UEcNSUHsc,2555
pyarrow/include/parquet/level_conversion.h,sha256=xWMMG8DFqxIx4EVevtiFdLtiZhKT3KajEd3EhIJv7lM,9648
pyarrow/include/parquet/level_conversion_inc.h,sha256=UjK4ppQFhMnQxXCIp1wm9HpdjXPpa-sOUQA0EG9AZkM,14515
pyarrow/include/parquet/metadata.h,sha256=Yzo4drQU-y4m3DAihkrguWqHRj4UDz6U3Gio8T7JMUI,21481
pyarrow/include/parquet/page_index.h,sha256=ZQRBnOYPCFU-HqisHz3PU6hfa74675Jm7VNWUiwYW-I,17693
pyarrow/include/parquet/parquet_version.h,sha256=zTuY-2WYLdruuhM146CGAz4P_OB-th6IpWQJTTFHgtM,1195
pyarrow/include/parquet/platform.h,sha256=louS4-l00S0cBiFHu6Pm-dn26qu7OuOs9WfIf__jRh0,4010
pyarrow/include/parquet/printer.h,sha256=TrztCNIK16tEjlaZhWch3jdTayHMPzjBdJaziVBefRk,1594
pyarrow/include/parquet/properties.h,sha256=JkQEx_N68B47iTIxdYyOr45DgQxYLXKCnUXy-A5c_yA,56564
pyarrow/include/parquet/schema.h,sha256=BruRfhUDpZgk6zDAnkACNXa8PZVEQC4zw_VCEdksfBk,18716
pyarrow/include/parquet/size_statistics.h,sha256=3Tj8RKUY1ghn5Z2sT5DzrBFOUZ_ArgahigdW-QjkYzI,4429
pyarrow/include/parquet/statistics.h,sha256=qY3w11PQlus9wWCHuK0OPiVzpfyg5m-uGv5LEZ-GpMc,15558
pyarrow/include/parquet/stream_reader.h,sha256=sJ_wO06AUopUtr24Y_O5FDCTQHdA_5A35HIx2Uq8AWs,9094
pyarrow/include/parquet/stream_writer.h,sha256=k7qykhypcl_c394biPzy3RaAxIDEcZNWCTwKGnQrMHk,7748
pyarrow/include/parquet/test_util.h,sha256=euMURd_yKucOLDHXVYM1OByHrUcsCoVef7JcfHuAk3E,34331
pyarrow/include/parquet/type_fwd.h,sha256=tzABNnpjucQJLhMoh0rDYPPWMkSOsKtfRxBf0sBgBYQ,3170
pyarrow/include/parquet/types.h,sha256=8uuJnLnzufK4WtPsTE3Ql1Wm02VvwvWlSyzukpR48HY,27889
pyarrow/include/parquet/windows_compatibility.h,sha256=UqmjdtDSUHYXXSN7FfO5KqS-92ukyDG6iKOThvjcZ14,918
pyarrow/include/parquet/windows_fixup.h,sha256=R0sEz8lear84QSG4IkpGM_OUTXtMOwHLXRNAToREh_4,1081
pyarrow/include/parquet/xxhasher.h,sha256=xEbgF2yqriXsdUJBP3KdjMb_0AfpTzx7z6ZlmRLeIOs,2124
pyarrow/includes/__init__.pxd,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyarrow/includes/common.pxd,sha256=9VL-bHI3mu_mqOiZfht5VhHicYtqYjkCWywdnzQxQCo,5105
pyarrow/includes/libarrow.pxd,sha256=ttos9LhvOUfX5m4nKQw271Gqu-J-9xGBcSUnG9tgBQ8,123081
pyarrow/includes/libarrow_acero.pxd,sha256=Es8B0rG40J8Od1YkSfUBF1g-n4PlsD0GEe6h-zDTXg0,5416
pyarrow/includes/libarrow_cuda.pxd,sha256=YKMKTSNWQlUvInVfakoe3uIh-jJjS-4QZ-tj2FHT7Ng,5051
pyarrow/includes/libarrow_dataset.pxd,sha256=ch1hiv_BeI4uvvaAyKc24YnD4NIdHptDtbGKo1oQ0us,17582
pyarrow/includes/libarrow_dataset_parquet.pxd,sha256=-YdHCkSBT1tspnqbSze69P6D4e7kqC7lzBr-rLOJXF8,4691
pyarrow/includes/libarrow_feather.pxd,sha256=hIVe5CqIhUjc2nvfq3vjo1XoJm5qaXNZ3a1RWw8zS_U,2190
pyarrow/includes/libarrow_flight.pxd,sha256=vnHKZ2MW01Z6IBKryiulFiqncO7hxwAw1Wqa6qH5HYk,25284
pyarrow/includes/libarrow_fs.pxd,sha256=sudZsVmHpTmt9mpLWdQOgAbni7C3-S7Yh7f-4yrwFX4,15743
pyarrow/includes/libarrow_python.pxd,sha256=IHL2-FOv5NyGRG9CQRsOWNW4bxi550dqblhIgEbdQxE,11691
pyarrow/includes/libarrow_substrait.pxd,sha256=j0pCpCI2ibMsMXAvQ-kXTyyNfHUv0A2odoBnoo084Hw,4161
pyarrow/includes/libgandiva.pxd,sha256=4MZoYNhY6EedN3et3tFvjVHMENJwPCwPYISK2fbR3EI,11828
pyarrow/includes/libparquet.pxd,sha256=PBG-3xMNOssFJTKljPE1vkSoMlLsjOuiaMBVZduM6L8,26833
pyarrow/includes/libparquet_encryption.pxd,sha256=GnD8COEaP3Pk3bBr7bcSE5zmS7SF7PQ69ud4nQ3FE50,6063
pyarrow/interchange/__init__.py,sha256=hFVl1PV1F4Ovb92_NbtKgDsrqRquxi82guDjtkK0CQE,865
pyarrow/interchange/__pycache__/__init__.cpython-313.pyc,,
pyarrow/interchange/__pycache__/buffer.cpython-313.pyc,,
pyarrow/interchange/__pycache__/column.cpython-313.pyc,,
pyarrow/interchange/__pycache__/dataframe.cpython-313.pyc,,
pyarrow/interchange/__pycache__/from_dataframe.cpython-313.pyc,,
pyarrow/interchange/buffer.py,sha256=YANe1dybRIzNzd5x3FGoQb0DPfmzyzZOfvFHDLXF5CI,3466
pyarrow/interchange/column.py,sha256=YIflPEoStWK8YOLQfcd788L4WfiJ763tpHv4L0wjEdM,19879
pyarrow/interchange/dataframe.py,sha256=tOTq0H1TBOfzFrEN10GPAJnF4iyopd1jF_zKamvMqRQ,8622
pyarrow/interchange/from_dataframe.py,sha256=lGy4ZHtP61gfCJQScVx4HwalvqjIhQU-Ar6saDJsE5Y,20323
pyarrow/io.pxi,sha256=7dLbWNhqDCMm163GSp09FfaD8dSsY6fBjitDnRdVEso,89140
pyarrow/ipc.pxi,sha256=AYZUV0bz9O-43lhHBRSElHsIELbcd6ci8-mLv_-3c8w,44253
pyarrow/ipc.py,sha256=zOt6WckF93DjQQC9AcVtBrtkNAt4SaLz9ZJO-TVqPNI,9883
pyarrow/json.py,sha256=bgpEq-S1JmTPNsmoBsBdz7R3A9Y3lwfpniQyadO8klI,888
pyarrow/jvm.py,sha256=onJUQO9Lh2tHs4ZChpr-Sx22kMVXN6E7YQcUw1R4c0o,9912
pyarrow/lib.cp313-win_amd64.pyd,sha256=7ooCOIp7GRPQgEH5GjEUlWWgSdmzjZ4AV--EuRHfPlA,3555840
pyarrow/lib.h,sha256=aWAOSlSScduvc3gGO6aSAWcNBl2QECWEdQAZb6mIl_U,4631
pyarrow/lib.pxd,sha256=3NagUzKdK-Wws_8BQ61befd4ivMhZswm8PeOscFO37A,18326
pyarrow/lib.pyx,sha256=4_IfhzUCb6TaIkrxaOMxREKQWGMVjAr7ld_JAxQBp48,6317
pyarrow/lib_api.h,sha256=4oiKWmdhyjn5OxDSBamrukHpWwQbUioDR7X82dhgdXs,19487
pyarrow/memory.pxi,sha256=svtItQHc0EHJNW1vcEzTwdyNwmde_5wndNGchR2NFHs,9102
pyarrow/orc.py,sha256=308LJbL35p0EyJoVm-C-Hr7yIxwPuS30hHFPGIk14OE,12986
pyarrow/pandas-shim.pxi,sha256=fm3xqPaFa6rN9nodZJPlvdD26aHgkA872P4SYE7COMU,9100
pyarrow/pandas_compat.py,sha256=5jf-u6_0ogD71xi7sNkSdLfZCJ4AHgE3o-4zVwioP2o,46784
pyarrow/parquet.dll,sha256=UFNPfmP5Qvsc2nPZEqFOEqSSgf-jxgHlEv5wNm9Xgkg,6291456
pyarrow/parquet.lib,sha256=Hcno0jmayS-6koRCAfJvxEsvoM7CvJP4wH6XEwBmHzQ,767916
pyarrow/parquet/__init__.py,sha256=MP8hmY5y1-LXoEUg6VinrGfThZkw1HAwUIL4ddrSAxQ,842
pyarrow/parquet/__pycache__/__init__.cpython-313.pyc,,
pyarrow/parquet/__pycache__/core.cpython-313.pyc,,
pyarrow/parquet/__pycache__/encryption.cpython-313.pyc,,
pyarrow/parquet/core.py,sha256=rXmfL5nKb6BpXXaBtNCDNwJiwqELhZ9JhlcbSY_u9nE,96397
pyarrow/parquet/encryption.py,sha256=8XjYgPw3KhU7eYuXj23pO3dJArm97v-1AAHt4WCfA3A,1176
pyarrow/public-api.pxi,sha256=eOStTQZatUyzYnR9ylr3sBzAEmJQqCSx4cRuXoTHHpU,14507
pyarrow/scalar.pxi,sha256=tfPmHcRiVUrUD1y0UaGKo--Rr5riKyQtnA1nS3Bzyew,53905
pyarrow/src/arrow/python/CMakeLists.txt,sha256=psEMQqIKWjI2J7OIx0vE9TYQUNXHfTWVdL9F6GVGrZI,874
pyarrow/src/arrow/python/api.h,sha256=Jmx_-qq-VIvz7SNMf2izoPHXVOMXsP6dmdU_RN57iQE,1208
pyarrow/src/arrow/python/arrow_to_pandas.cc,sha256=rZhtK06hPejmwdlHS2-tNvPQ4aMjb6-hudZcEFWf8Ks,98228
pyarrow/src/arrow/python/arrow_to_pandas.h,sha256=N-Bl98Z-Y7OrkYzI8pJ5bhR6i-HoCom-Mk1sYCosRAo,5707
pyarrow/src/arrow/python/arrow_to_python_internal.h,sha256=DbOoJJai8YeS1JKBCloQlgzztwv1Kkjv3rF86nUK7ww,1789
pyarrow/src/arrow/python/async.h,sha256=15MshOaSHLSsfKlQw4LCY338khPideC74IpLOFCygDE,2412
pyarrow/src/arrow/python/benchmark.cc,sha256=-bPstUW6eQ843nOduN6dFYUfYaWeJlL8PctHJZkRQYI,1331
pyarrow/src/arrow/python/benchmark.h,sha256=OqCsqRe5osY2lhe2ecN8cXrdkmSFgLp6fXcf4_N9Fik,1228
pyarrow/src/arrow/python/common.cc,sha256=rrIKLF0wNIzHL3VBo2RAObt2DOz8wF1ocNXyQftISPw,7849
pyarrow/src/arrow/python/common.h,sha256=VJMEOTBDNX0uY2wFYK-QSpAHoixaufKA7PhbE1uwPY0,14846
pyarrow/src/arrow/python/csv.cc,sha256=sosW1I3yNLLmoScv8o8ECYzLUB8ZiEboeDAstC8SVZg,1865
pyarrow/src/arrow/python/csv.h,sha256=7yixEpKzdR_wvjfG0rlkmtYpSyjEuNb2cPLueyKFa0s,1439
pyarrow/src/arrow/python/datetime.cc,sha256=ePcPl9ehdBNT52_Uy0CuOare7UOr427T9bhWA8Y3uII,23678
pyarrow/src/arrow/python/datetime.h,sha256=rPOB2fYu3E7_Lb11RdXglv1tShjA_k52cyz4zw481Q8,8162
pyarrow/src/arrow/python/decimal.cc,sha256=6xexm_6y4Bjb2mE06awl6ZpdrBWFQaC5sfoIXpMJfUI,9964
pyarrow/src/arrow/python/decimal.h,sha256=cBQOd-yW1ArcUf29lPgEgBUI6I3cCcWT4B6uwd7fTbw,6524
pyarrow/src/arrow/python/extension_type.cc,sha256=tWs9KkzmJzhAuXDXl9AnFL076hA86Z4rksNrUAnWXHg,7101
pyarrow/src/arrow/python/extension_type.h,sha256=ovyhXsE-iornOx99_iOmQjZbdqIIpzUfiT7dTvVjQro,3266
pyarrow/src/arrow/python/filesystem.cc,sha256=TF2i8-laHGZJgDizwsMMBxPi0gTJEiC8RB9A6MK_zfw,6358
pyarrow/src/arrow/python/filesystem.h,sha256=bnXcXSssY90VwD784m3CqQuNnZdwZxKpc8MltD4zJyY,5256
pyarrow/src/arrow/python/flight.cc,sha256=nZ5HZHWjzrkcEAnZaMrNJezV85fe7e8pSOauKfm0Rm4,14582
pyarrow/src/arrow/python/flight.h,sha256=dJ6L40ZbpUVKbyc-DytLQFaiUC9rotWFi3ofA5oX3LQ,14802
pyarrow/src/arrow/python/gdb.cc,sha256=j0WnHtf1h7Agl2mbf1SJLQrEMrtE9GvQtUthzCRRLLo,23061
pyarrow/src/arrow/python/gdb.h,sha256=VN8aDtc1iFPIKsjFQ29RKQOkClvKAAI_KeTy_LFZcZg,1001
pyarrow/src/arrow/python/helpers.cc,sha256=LOk4IrdZm86iSQ9qKeN8Hvo7MjEpvzg4eN-wdtYMEGA,17438
pyarrow/src/arrow/python/helpers.h,sha256=VTge6_S23-R8gKOvF8R_M11_FX-61POZMrAxGmHlvEw,5639
pyarrow/src/arrow/python/inference.cc,sha256=19SiHzwXBQBaqM3eX_Lmbwz2EP0gSX0aZdHJ973NhPA,25095
pyarrow/src/arrow/python/inference.h,sha256=_44xuq5q-qktVBIigUbwmlgzOMJ4vHuauXQu-pXG1bg,2102
pyarrow/src/arrow/python/io.cc,sha256=hp3ckwW-K0Da8dFgNB4lus6rutgpqHq4mJ0ZkJYDOz0,12335
pyarrow/src/arrow/python/io.h,sha256=byOtFjW3NuetpJIErXFG62bsim0ZsWIAPgbes_JvI3k,3979
pyarrow/src/arrow/python/ipc.cc,sha256=0j0rCBxCObGLNKEksCFxnuLqJVtscnpMeOS_kyT7Jus,4607
pyarrow/src/arrow/python/ipc.h,sha256=lHD6Yl4f_ARUIK6SlA4Jy75i7_K940NIGg6vHauLypc,2331
pyarrow/src/arrow/python/iterators.h,sha256=9aFm6h4iTZArpr0jSCx8PNIpzmPhmHgZENio6Ykyx6w,7527
pyarrow/src/arrow/python/numpy_convert.cc,sha256=UOhnPx7spEi2Hlq0PJz8FaiEcsCZ8m8xPoNsoLVJ6Xs,21757
pyarrow/src/arrow/python/numpy_convert.h,sha256=JVUlnUuzWE4OwsoHPl4hvzi5_nkDjHR_wO5Iu8ZEVkU,4992
pyarrow/src/arrow/python/numpy_init.cc,sha256=539UpFy5xAvGyZ9OzjHRzG4bukjaFEcMmSeATD2Rq48,1211
pyarrow/src/arrow/python/numpy_init.h,sha256=e4zEoclXjOzRydJi6iEa0SQZoBL2xax_odZ2JApkKlk,1026
pyarrow/src/arrow/python/numpy_internal.h,sha256=Sk_AABqLkOCNoc0zWHR1eU2vAjac4IXfNK4TggLZcGg,5515
pyarrow/src/arrow/python/numpy_interop.h,sha256=NMBHwZjVHiOGujS-223OxV9V5l0niyHX3-Qw1MZ-4kU,3521
pyarrow/src/arrow/python/numpy_to_arrow.cc,sha256=aIt4pG0GMaJX-CD-Zmxxc7nPcbxTENQ_IW0CjiciJ7A,32906
pyarrow/src/arrow/python/numpy_to_arrow.h,sha256=-JhqPEg-tOBKhwRDhurxrAYQ6I-ws4CoghILzj9R-ms,2832
pyarrow/src/arrow/python/parquet_encryption.cc,sha256=RFWN7nVzsScOor6yQWkGHK9YCeDIXOrHa4bGBdyNGBE,3665
pyarrow/src/arrow/python/parquet_encryption.h,sha256=2d1-5CgmUZOFIXscD4J2nxuOkEhac0opO0PB3Jp2uz4,4993
pyarrow/src/arrow/python/platform.h,sha256=Otng5ebgyLptzmXIjS9o7tLS71emaH1doLGpDjYDxso,1463
pyarrow/src/arrow/python/pyarrow.cc,sha256=Lk8HHlcIzsSg4LTnzS6FW0yzAZgWuH0rZSBTI8i75Gc,3783
pyarrow/src/arrow/python/pyarrow.h,sha256=Pt2advP3jdTBRb5y2TtyDLVBACieOKI2IZblGu6kPMc,2850
pyarrow/src/arrow/python/pyarrow_api.h,sha256=uPrpVqzif4NlQkJvj-tZCIeBLBLSNE7ekGjKlCPCZxw,886
pyarrow/src/arrow/python/pyarrow_lib.h,sha256=D1QHgdorjjbTko9iMJLaFwykjnjIRBDLyuQaskohSmw,882
pyarrow/src/arrow/python/python_test.cc,sha256=Hmx4UAaH9bilzE1dM8-QSJVmAF3jU5wUCRBA_qTqr8o,33213
pyarrow/src/arrow/python/python_test.h,sha256=kdEUk3TAKggBdER-tT8y-fIcO2UnfEh2K8XFrnDYLYk,1237
pyarrow/src/arrow/python/python_to_arrow.cc,sha256=7WzrgdxsTZwsoILHpoSqd_ZiMdIAZ5cY2AEfpoxSJfM,49199
pyarrow/src/arrow/python/python_to_arrow.h,sha256=r6iWOjXDn6gO6Qxo9mAlnm6f94jdAE8EmW0D23UJgac,2601
pyarrow/src/arrow/python/type_traits.h,sha256=UPe0QkAuIDKJAgTNmMxIehMePjnl7_rmsJEk6mCONKs,10589
pyarrow/src/arrow/python/udf.cc,sha256=PcG1lpWvbT0fsq27qbOHDqJrbCBgOHK7GuZvagsAztA,30527
pyarrow/src/arrow/python/udf.h,sha256=gLQGE1vYHlYJ_ceN1Kb0upw6cP90juYZ4b30VvuUPPM,3185
pyarrow/src/arrow/python/util.cc,sha256=nfD1-SvggvaBJstLZw0I3F6SqNw2LVmN8FgAJ9f10To,1902
pyarrow/src/arrow/python/util.h,sha256=rUm_q03_LQ7r21r5GDrr1iG7pPzU7X1k9yxkF_IIiAY,1772
pyarrow/src/arrow/python/vendored/CMakeLists.txt,sha256=6MYldTCwSTzrxbHLzePRpUDdlKCJTBqLUd92X3aioOU,855
pyarrow/src/arrow/python/vendored/pythoncapi_compat.h,sha256=5TMOeKnV-Fpx4R1WeDeJz-3EvvLWyrgGZyCLNMTVVqs,42419
pyarrow/src/arrow/python/visibility.h,sha256=yIEoKC8dabjDOxjUdLB6CiP82nhFkyGdudhLyJNgmvU,1420
pyarrow/substrait.py,sha256=axTfPnyS2kGd2w6I1mJIAFSHSGlcY47zBF-S_Gv1a8c,1263
pyarrow/table.pxi,sha256=s1_V3UxLsRtxGLHahUMO5qfohPWiQ3ev2VmdatRFqlU,215183
pyarrow/tensor.pxi,sha256=Hogo0PtMlv8M8OsHkrorG83iQMQ-vTk7OK-DOF8LRZ4,45012
pyarrow/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyarrow/tests/__pycache__/__init__.cpython-313.pyc,,
pyarrow/tests/__pycache__/arrow_16597.cpython-313.pyc,,
pyarrow/tests/__pycache__/arrow_39313.cpython-313.pyc,,
pyarrow/tests/__pycache__/arrow_7980.cpython-313.pyc,,
pyarrow/tests/__pycache__/conftest.cpython-313.pyc,,
pyarrow/tests/__pycache__/pandas_examples.cpython-313.pyc,,
pyarrow/tests/__pycache__/pandas_threaded_import.cpython-313.pyc,,
pyarrow/tests/__pycache__/read_record_batch.cpython-313.pyc,,
pyarrow/tests/__pycache__/strategies.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_acero.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_adhoc_memory_leak.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_array.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_builder.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_cffi.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_compute.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_convert_builtin.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_cpp_internals.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_csv.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_cuda.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_cuda_numba_interop.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_cython.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_dataset.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_dataset_encryption.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_deprecations.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_device.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_dlpack.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_exec_plan.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_extension_type.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_feather.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_flight.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_flight_async.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_fs.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_gandiva.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_gdb.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_io.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_ipc.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_json.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_jvm.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_memory.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_misc.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_orc.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_pandas.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_scalars.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_schema.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_sparse_tensor.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_strategies.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_substrait.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_table.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_tensor.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_types.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_udf.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_util.cpython-313.pyc,,
pyarrow/tests/__pycache__/test_without_numpy.cpython-313.pyc,,
pyarrow/tests/__pycache__/util.cpython-313.pyc,,
pyarrow/tests/__pycache__/wsgi_examples.cpython-313.pyc,,
pyarrow/tests/arrow_16597.py,sha256=4MtaNkHRXf-ae8UPc_W0Sy7PQOSA38L2znT2LE0YSxM,1391
pyarrow/tests/arrow_39313.py,sha256=6iDq6Ci3q_SFDjA7RtZ27cnwmlyqGQQJgzThU5YPjG8,1478
pyarrow/tests/arrow_7980.py,sha256=W3Gbd_XbB9vC87k4VDIjyJgGfUurz6PKkFJn1pNdR4I,1124
pyarrow/tests/bound_function_visit_strings.pyx,sha256=QgKbbEM5Tm7ZNP4TTiEwpSut-sP0KEE7eT3K0KgRTw8,2093
pyarrow/tests/conftest.py,sha256=4UotY07XYGRiMmXAOcfigK5mRt8vOYGTUWm_p5tM3ec,10716
pyarrow/tests/data/feather/v0.17.0.version.2-compression.lz4.feather,sha256=qzcc7Bo4OWBXYsyyKdDJwdTRstMqB1Zz0GiGYtndBnE,594
pyarrow/tests/data/orc/README.md,sha256=6J34Nh2eK930SY4vBkZbTlTnyC7ImMmVuJOFRIY8yDI,954
pyarrow/tests/data/orc/TestOrcFile.emptyFile.jsn.gz,sha256=xLjAXd-3scx3DCyeAsmxTO3dv1cj9KRvYopKe5rQNiI,50
pyarrow/tests/data/orc/TestOrcFile.emptyFile.orc,sha256=zj0579dQBXhF7JuB-ZphkmQ81ybLo6Ca4zPV4HXoImY,523
pyarrow/tests/data/orc/TestOrcFile.test1.jsn.gz,sha256=kLxmwMVHtfzpHqBztFjfY_PTCloaXpfHq9DDDszb8Wk,323
pyarrow/tests/data/orc/TestOrcFile.test1.orc,sha256=A4JxgMCffTkz9-XT1QT1tg2TlYZRRz1g7iIMmqzovqA,1711
pyarrow/tests/data/orc/TestOrcFile.testDate1900.jsn.gz,sha256=oWf7eBR3ZtOA91OTvdeQJYos1an56msGsJwhGOan3lo,182453
pyarrow/tests/data/orc/TestOrcFile.testDate1900.orc,sha256=nYsVYhUGGOL80gHj37si_vX0dh8QhIMSeU4sHjNideM,30941
pyarrow/tests/data/orc/decimal.jsn.gz,sha256=kTEyYdPDAASFUX8Niyry5mRDF-Y-LsrhSAjbu453mvA,19313
pyarrow/tests/data/orc/decimal.orc,sha256=W5cV2WdLy4OrSTnd_Qv5ntphG4TcB-MyG4UpRFwSxJY,16337
pyarrow/tests/data/parquet/v0.7.1.all-named-index.parquet,sha256=YPGUXtw-TsOPbiNDieZHobNp3or7nHhAxJGjmIDAyqE,3948
pyarrow/tests/data/parquet/v0.7.1.column-metadata-handling.parquet,sha256=7sebZgpfdcP37QksT3FhDL6vOA9gR6GBaq44NCVtOYw,2012
pyarrow/tests/data/parquet/v0.7.1.parquet,sha256=vmdzhIzpBbmRkq3Gjww7KqurfSFNtQuSpSIDeQVmqys,4372
pyarrow/tests/data/parquet/v0.7.1.some-named-index.parquet,sha256=VGgSjqihCRtdBxlUcfP5s3BSR7aUQKukW-bGgJLf_HY,4008
pyarrow/tests/extensions.pyx,sha256=Je7YHLuhd4fMQMn4GVPfAiyz8GPXBtX1z6pgclnyEq8,3140
pyarrow/tests/interchange/__init__.py,sha256=EVfaECqIP6zwm3-KeFxrnNB3-C4waxZUgr3MmUMTU6k,801
pyarrow/tests/interchange/__pycache__/__init__.cpython-313.pyc,,
pyarrow/tests/interchange/__pycache__/test_conversion.cpython-313.pyc,,
pyarrow/tests/interchange/__pycache__/test_interchange_spec.cpython-313.pyc,,
pyarrow/tests/interchange/test_conversion.py,sha256=CRhbFuV4WanciMhwvZSxYgphKenATeMx3yS1tppCK30,19138
pyarrow/tests/interchange/test_interchange_spec.py,sha256=LDSBpk6LgL1Rv3krxMrQnkT8QWAiVkfMFdtfslu3b6E,9675
pyarrow/tests/pandas_examples.py,sha256=9e910a13lzzl2U7bllcEL-IWQyzDcwGSMRU6ZgOU_V8,5279
pyarrow/tests/pandas_threaded_import.py,sha256=TkyeVQWGZNoRUgbx39KC5ENTV8q3bMmo9wJxIIxso0Y,1473
pyarrow/tests/parquet/__init__.py,sha256=HBk5_BESLXOn2axHI96jkaiO9dkNzfXHZAUPf0sXbgM,955
pyarrow/tests/parquet/__pycache__/__init__.cpython-313.pyc,,
pyarrow/tests/parquet/__pycache__/common.cpython-313.pyc,,
pyarrow/tests/parquet/__pycache__/conftest.cpython-313.pyc,,
pyarrow/tests/parquet/__pycache__/encryption.cpython-313.pyc,,
pyarrow/tests/parquet/__pycache__/test_basic.cpython-313.pyc,,
pyarrow/tests/parquet/__pycache__/test_compliant_nested_type.cpython-313.pyc,,
pyarrow/tests/parquet/__pycache__/test_data_types.cpython-313.pyc,,
pyarrow/tests/parquet/__pycache__/test_dataset.cpython-313.pyc,,
pyarrow/tests/parquet/__pycache__/test_datetime.cpython-313.pyc,,
pyarrow/tests/parquet/__pycache__/test_encryption.cpython-313.pyc,,
pyarrow/tests/parquet/__pycache__/test_metadata.cpython-313.pyc,,
pyarrow/tests/parquet/__pycache__/test_pandas.cpython-313.pyc,,
pyarrow/tests/parquet/__pycache__/test_parquet_file.cpython-313.pyc,,
pyarrow/tests/parquet/__pycache__/test_parquet_writer.cpython-313.pyc,,
pyarrow/tests/parquet/common.py,sha256=7I3X7XdArUpNEBjQ4Yfa7PX3RPHc2hrQE3SnsLz0pik,6153
pyarrow/tests/parquet/conftest.py,sha256=PmW8hN_QHvXKGteGmTDetehGh9BnFIUm2Glk8ww9aNA,3135
pyarrow/tests/parquet/encryption.py,sha256=0Zoz4i_eIEF7SepbogLZg8dWKprEw0IjgRmh-xu_vNk,2582
pyarrow/tests/parquet/test_basic.py,sha256=huS6Ba4Rw6VUhG5KyZ9zb_YUfPa1DLBX6wI6NHg39mM,37765
pyarrow/tests/parquet/test_compliant_nested_type.py,sha256=gL9xcmd1IlVPXF68-pULYfryIrGcJI40NOd2btKH3mM,4010
pyarrow/tests/parquet/test_data_types.py,sha256=DpuaVxWU-dRfLKu_sTumTlu8OrCjUsFX_ii-hi-z1jg,19935
pyarrow/tests/parquet/test_dataset.py,sha256=IDJhFkq-XLzRRRR0BDX7uYmkCkbbO56cnmMaP6xZ6Vc,43464
pyarrow/tests/parquet/test_datetime.py,sha256=g8vMoLGABOs3MgRm1-Pm3QW8fEkRHV5EMW3NXTaIr3Q,16859
pyarrow/tests/parquet/test_encryption.py,sha256=kYoVzrNx3M41dR7pKf_YbsL700lNUl1VUDg_jA4ZgIo,24623
pyarrow/tests/parquet/test_metadata.py,sha256=lPWbpVmQCKhnZDfYerbOQd3RQ8DhtQSexv7VLczKi7o,28548
pyarrow/tests/parquet/test_pandas.py,sha256=9GmCnCQoSrvzsVZFAXnhl_P04HedPBeV8OhH4w4C1As,23329
pyarrow/tests/parquet/test_parquet_file.py,sha256=gdeeuKQWY_c1bnudWEnPSFBHD5BaCeVOxIs4igYDXrc,14114
pyarrow/tests/parquet/test_parquet_writer.py,sha256=cDBEFCReX9KHb-aB7VYpFXgNijixjn3yNBL_9fzOe04,16365
pyarrow/tests/pyarrow_cython_example.pyx,sha256=Qgd7BEHzrxDVpdMzLplpD-XAkqN0Vu6-N75KGhLXkpQ,2176
pyarrow/tests/read_record_batch.py,sha256=J8MvpGBAv7rjvoseM5OnQyU9BL_3f2Ffdc0cYFvLh60,978
pyarrow/tests/strategies.py,sha256=eO1-NPpT8VnEZZ9s7aPJ4O_V5qOme_16Bi_3FOwY_eg,14699
pyarrow/tests/test_acero.py,sha256=GkMigF-nC9-lvYydFqALLPAF4Ffg6dsmAC2Y5F_As7Y,18505
pyarrow/tests/test_adhoc_memory_leak.py,sha256=AjG44vFpuJfzGoIT58TmFFkncVdBpXQJzLJkEokcbNg,1499
pyarrow/tests/test_array.py,sha256=p9kp8aHeyHUv-0UJ98XqO8x1fzZcgMCikP5nFLanyk0,147963
pyarrow/tests/test_builder.py,sha256=tLnE5vjV_DKUNhMs97s2BHjHKhgkwUSE4yMBJwgxniQ,2888
pyarrow/tests/test_cffi.py,sha256=8elpKAxXYZQEe5teru3cOciMr0QHu0RJv1of5mbwO0Q,27156
pyarrow/tests/test_compute.py,sha256=MnwhnaRLHrJZnWhCTjC-m9qfkp2vzlw79dHqjnRa12g,157706
pyarrow/tests/test_convert_builtin.py,sha256=4GpDLXApVjTPuRdZFGX3e6WVRp26ocJkGSxIn9urV6I,83571
pyarrow/tests/test_cpp_internals.py,sha256=IDCFZdVZRXwsu-brGSI1wYgvkf1QHlzx271UGQYAyFE,2064
pyarrow/tests/test_csv.py,sha256=d2lFUxyrZz9vTCtSNqYgFDb5ksqyketfOqXbJ-GeOds,79343
pyarrow/tests/test_cuda.py,sha256=RZAUbTT04W8IN5S0WxlVKcAkhDic1prI1sx0Pxw6W7U,37184
pyarrow/tests/test_cuda_numba_interop.py,sha256=zrd4EqaL3jA4R1CNqXEOVJt5TyX1a-gi_vitoJ1o364,9032
pyarrow/tests/test_cython.py,sha256=aS9Wfn7p9z9ppqyrUxkr9Np__TVvVWJdo-UNuJhQpcc,7243
pyarrow/tests/test_dataset.py,sha256=uZiLqUdOxtTEnBgQw-2A2U4AwwgsfP13iA6TQumFIEc,220279
pyarrow/tests/test_dataset_encryption.py,sha256=ykxgeSMm3FijNtWfIJDFjSfotd0HQs-PGEl6MMaca_Q,7825
pyarrow/tests/test_deprecations.py,sha256=qGsfYqoc2Ty5l3fIKL7zCM_IeGFgquPPv_5OWRAA6UM,914
pyarrow/tests/test_device.py,sha256=E1ElShO0Q_cBs63mtcVEsD3ARxN2c-Z2su1fJX-U2yU,2619
pyarrow/tests/test_dlpack.py,sha256=U0B29LPP_lo6WB4HuLENZLWD8FdcoqIKnxeaNb6SDUM,6148
pyarrow/tests/test_exec_plan.py,sha256=bdEJdN7i5hc6qRKA-GTt5p9KHRqrsfaGjZCQL_XB9Qw,10433
pyarrow/tests/test_extension_type.py,sha256=LsC5Svw2EapcoivUB9ApPCA5vBydMt1E5EdlYKAudTw,69536
pyarrow/tests/test_feather.py,sha256=ujNIBYAilA18-nezVUyKyDj4dIcqzYZONhzXg_8b6jU,26500
pyarrow/tests/test_flight.py,sha256=w-2rDAgzcT9OIFcHZGgEDSqv0kNwuksw7bvrLUUBy1k,95369
pyarrow/tests/test_flight_async.py,sha256=6voht5atX8oC2fZv18LFAnoR2695_ip8zAkFiuiNqqs,2939
pyarrow/tests/test_fs.py,sha256=egITpp_rYFXgQval7KYXy6wwy1sJHyOjtGJjoopQPOw,72325
pyarrow/tests/test_gandiva.py,sha256=9DgDgJyjl7EnXAMYBgOqABUbCeDc723W6lmLmfzft84,16057
pyarrow/tests/test_gdb.py,sha256=suiTzWyh_8ZJH1URl3bcnU2pgPwqPdgqVKFeCZCLEcs,46020
pyarrow/tests/test_io.py,sha256=Fjdc3dVlKB_9Y9FJlJMhhbHvfJGcEVogAkDthPbBR-8,65948
pyarrow/tests/test_ipc.py,sha256=FzFCJQ5z8UCX6FFkZL5zebaGZHAaW7iP-SdhnMykDJA,44732
pyarrow/tests/test_json.py,sha256=U0W3UUxUMwebYH_GRx4pyGDcPbsR13yO4cs_-UGkP-0,23624
pyarrow/tests/test_jvm.py,sha256=u7doo6NFP-_JUIwb338e_iNOj4KzAsz_kYYzwrQ2MYE,15910
pyarrow/tests/test_memory.py,sha256=ADbzKXGNklMPQP6nOWahZZTCs61igudOdZ54e7opI5Y,10156
pyarrow/tests/test_misc.py,sha256=0vIgHeREZuLJsAxoRBvXO9WTUZdFk50rguH0qbuU8VY,7576
pyarrow/tests/test_orc.py,sha256=5wUhv8IKgCzwkaoKio_TlMpiv6OTIYXWTgiqwmnFTuc,21877
pyarrow/tests/test_pandas.py,sha256=ZOhhomZi64cRkPDZpO6pGbeXKWssnV0ZmYkA-fjCfXc,198378
pyarrow/tests/test_scalars.py,sha256=9vi3dk-AW8-bR8afI1vkLfvc2pkTdiPDBBmJQGSxH-k,30545
pyarrow/tests/test_schema.py,sha256=AFJABkd5x23hXUjaeb1CnmFAmL4HP-MncPMG1PCkzek,23394
pyarrow/tests/test_sparse_tensor.py,sha256=eoVNpUU07w7u136X5w3PHTdPXddyN5zHw1byMq8WpX0,18163
pyarrow/tests/test_strategies.py,sha256=0GNbCJiFb3PxZtw-0Ou6hLO1jmgj771yrMGq41xRhOE,1895
pyarrow/tests/test_substrait.py,sha256=b7ANCBZiTEbGJOcCVRtaNg1pfFIUxd_vc0Et2R6_RZ4,33343
pyarrow/tests/test_table.py,sha256=jlTjTDvc8UZiIa1NzhxbI5qi0ST4XVBnA_7yprX7J-A,127933
pyarrow/tests/test_tensor.py,sha256=-Cy3GoHMC4oaqQExN9BKdN_0aHdw6ZJOV3fkpdeF5Fg,6869
pyarrow/tests/test_types.py,sha256=-pFdqwVispGwyP6d4Cv_pBwPe88SFtMccd9PB98fofU,45269
pyarrow/tests/test_udf.py,sha256=7tl7W8Hur8lpeD0TcPQyY_MNHP95DB1osYgUzwVwbpY,30703
pyarrow/tests/test_util.py,sha256=NAENcrCOT9uTSpoU8KqW67e1H6tJP5COkGlAZeAAX48,5259
pyarrow/tests/test_without_numpy.py,sha256=69gobJK2_fq37P_ffFG1tgRIiopjqMY2MrOiPGOlYvQ,1913
pyarrow/tests/util.py,sha256=uLwS-srqOureBgDu1z1mhyB1pR_Z5cDEN4SwUXkp13k,13932
pyarrow/tests/wsgi_examples.py,sha256=uLr0kLfVuPdMVl5wY-0_sT-lDlFJhEbT5zqbIVKAK0A,1383
pyarrow/types.pxi,sha256=tUJDZlfb_m-et9iEYZg2p8HMwabSjGRYafHC1_wPdzg,168571
pyarrow/types.py,sha256=AaFh8ZjGdF-FYLKx-agP0hlmkPXSGn2BThBy5Nzl4uU,7817
pyarrow/util.py,sha256=RdjQYJUT8_08Sf45kyTHtuMFks1uoH2nmIUGRCU0jFY,8916
pyarrow/vendored/__init__.py,sha256=EVfaECqIP6zwm3-KeFxrnNB3-C4waxZUgr3MmUMTU6k,801
pyarrow/vendored/__pycache__/__init__.cpython-313.pyc,,
pyarrow/vendored/__pycache__/docscrape.cpython-313.pyc,,
pyarrow/vendored/__pycache__/version.cpython-313.pyc,,
pyarrow/vendored/docscrape.py,sha256=QgFpJRG3lyDx6Q0sUuqkkowQ23up97bT_LlgkqG5K40,23691
pyarrow/vendored/version.py,sha256=cr57fm516rcswYHhTfyRDXt-yDWq1EAlRTrZz3mVHo8,14890
