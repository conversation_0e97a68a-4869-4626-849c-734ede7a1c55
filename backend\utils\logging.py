import sys
from loguru import logger
from pathlib import Path
from config import settings

def setup_logging():
    """Configure structured logging for the application"""
    
    # Remove default logger
    logger.remove()
    
    # Console logging
    logger.add(
        sys.stdout,
        level=settings.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        colorize=True
    )
    
    # File logging
    log_dir = Path("./logs")
    log_dir.mkdir(exist_ok=True)
    
    # Application logs
    logger.add(
        log_dir / "app.log",
        level="INFO",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="1 day",
        retention="30 days",
        compression="zip"
    )
    
    # Error logs
    logger.add(
        log_dir / "errors.log",
        level="ERROR",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="1 week",
        retention="12 weeks",
        compression="zip"
    )
    
    # Security audit logs
    logger.add(
        log_dir / "security.log",
        level="WARNING",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="1 day",
        retention="90 days",
        compression="zip",
        filter=lambda record: "security" in record["extra"]
    )
    
    logger.info("Logging system initialized")

# Export logger for use in other modules
__all__ = ["logger", "setup_logging"]
