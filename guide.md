# Psychiatric ML Data Collection System - Core Specification

## Project Overview

Build a **high-performance, privacy-first psychiatric data collection application** that serves dual purposes:
1. **Training Phase**: Collect structured clinical data for ML model development
2. **Inference Phase**: Use trained models for real-time clinical decision support during patient history taking

### Key Design Principles
- **Privacy-by-Design**: Zero PII storage, encrypted pseudonymous identifiers only
- **Offline-First**: Complete local operation, no network dependencies
- **ML-Optimized**: Data structures designed for feature engineering and model training
- **Clinical Workflow**: Intuitive interface matching psychiatric assessment patterns
- **Performance**: Sub-second response times, minimal resource usage

---

## Technical Stack

### Core Framework
```
Python 3.11+
├── FastAPI + Uvicorn - High-performance async API
├── SQLite + SQLAlchemy 2.0 - Local database with async support
├── Pydantic v2 - Data validation and serialization
├── React/TypeScript - Modern web UI (via FastAPI static serving)
├── TailwindCSS - Utility-first styling
└── scikit-learn/PyTorch - ML pipeline integration
```

### Why This Stack?
- **FastAPI**: Native async support, automatic OpenAPI docs, excellent performance
- **SQLite**: Zero-configuration, ACID compliance, perfect for local deployment
- **React**: Rich ecosystem, excellent TypeScript support, component reusability
- **Pydantic**: Runtime validation, automatic JSON schema generation, ML serialization

---

## Performance Requirements

### Startup & Response Times
- **Cold start**: <2 seconds from launch to UI ready
- **Form rendering**: <100ms for any assessment screen
- **Data persistence**: <50ms for form submissions
- **ML inference**: <500ms for real-time predictions
- **Export generation**: <3 seconds for full dataset (1000+ patients)

### Resource Constraints
- **Memory usage**: <150MB during normal operation
- **Storage**: <50MB for application, <100MB for 5000+ patient records
- **CPU**: Efficient on single-core systems, scales to multi-core

---

## Data Architecture

### Patient Identification System
```python
class PatientIdentifier:
    """Encrypted pseudonymous identifier system"""
    
    @staticmethod
    def generate_pid(original_id: str, clinic_key: str) -> str:
        """Generate irreversible pseudonymous ID"""
        # HMAC-SHA256 with clinic-specific key
        # Base64 encoding with CRC32 checksum
        # Format: PID_[16char_hash][4char_checksum]
        
    @staticmethod  
    def validate_pid_format(pid: str) -> bool:
        """Validate PID structure without decryption"""
        # Verify format and checksum integrity
        # Never store or log the validation process
```

### Core Data Models

#### 1. Patient Demographics
```python
class PatientDemographics(BaseModel):
    pid: str = Field(..., regex=r'^PID_[A-Za-z0-9+/]{20}$')
    
    # Categorical fields optimized for ML
    age_group: Literal["18-25", "26-35", "36-45", "46-55", "56-65", "65+"]
    gender: Literal["Male", "Female", "Non-binary", "Other", "Undisclosed"]
    education: Literal["Primary", "Secondary", "Bachelor", "Graduate", "Professional"]
    occupation: Literal["Healthcare", "Education", "Technology", "Service", "Manual", "Student", "Unemployed", "Retired", "Other"]
    marital_status: Literal["Single", "Married", "Divorced", "Widowed", "Separated"]
    living_situation: Literal["Alone", "Family", "Shared", "Assisted", "Institutional"]
    
    # Clinical context
    referral_source: Literal["Self", "GP", "Specialist", "Emergency", "Court", "Other"]
    insurance_type: Literal["Private", "Public", "Uninsured", "Unknown"]
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.now)
    clinician_id: str
    data_quality_score: float = Field(default=1.0, ge=0.0, le=1.0)
```

#### 2. Psychiatric Assessment Matrix
```python
class SymptomAssessment(BaseModel):
    """DSM-5-TR aligned symptom assessment"""
    pid: str
    assessment_date: datetime = Field(default_factory=datetime.now)
    
    # Major symptom domains (each with severity + duration)
    mood_symptoms: Dict[str, SymptomItem]
    psychotic_symptoms: Dict[str, SymptomItem]  
    anxiety_symptoms: Dict[str, SymptomItem]
    cognitive_symptoms: Dict[str, SymptomItem]
    behavioral_symptoms: Dict[str, SymptomItem]
    
    # Global assessment
    functioning_level: int = Field(..., ge=1, le=100)  # GAF-style score
    symptom_severity_index: float = Field(..., ge=0.0, le=10.0)
    
class SymptomItem(BaseModel):
    present: bool = False
    severity: Optional[Literal["Mild", "Moderate", "Severe"]] = None
    duration_weeks: Optional[int] = None
    functional_impact: Optional[Literal["None", "Mild", "Moderate", "Severe"]] = None
    
    @model_validator(mode='after')
    def validate_symptom_logic(self):
        if not self.present:
            self.severity = None
            self.duration_weeks = None
            self.functional_impact = None
        return self
```

#### 3. Clinical History & Risk Factors
```python
class ClinicalHistory(BaseModel):
    pid: str
    
    # Psychiatric history
    previous_diagnoses: List[str] = []  # ICD-11 codes
    hospitalization_count: int = 0
    suicide_attempts: int = 0
    medication_trials: Dict[str, str] = {}  # drug_class -> response
    therapy_history: List[str] = []  # CBT, DBT, etc.
    
    # Family & genetic factors
    family_psychiatric_history: bool = False
    family_conditions: List[str] = []
    
    # Substance use (structured for ML)
    substance_use: Dict[str, SubstanceUseItem]
    
    # Medical comorbidities
    medical_conditions: List[str] = []
    current_medications: List[str] = []
    
    # Social determinants
    trauma_history: bool = False
    social_support_level: Literal["High", "Moderate", "Low", "Minimal"]
    housing_stability: Literal["Stable", "Temporary", "Unstable", "Homeless"]
    
class SubstanceUseItem(BaseModel):
    current_use: bool = False
    frequency: Optional[Literal["Daily", "Weekly", "Monthly", "Occasional"]] = None
    severity: Optional[Literal["Mild", "Moderate", "Severe"]] = None
    years_of_use: Optional[int] = None
```

#### 4. Laboratory & Biomarker Data
```python
class LabResults(BaseModel):
    """Structured lab data with ML preprocessing"""
    pid: str
    collection_date: datetime
    lab_type: Literal["Basic_Metabolic", "CBC", "Liver_Function", "Thyroid", "Toxicology", "Inflammatory_Markers"]
    
    # Raw values with units
    raw_values: Dict[str, Optional[float]]
    reference_ranges: Dict[str, Tuple[float, float]]
    
    # ML-ready features
    normalized_values: Dict[str, Optional[float]] = {}  # Z-scores
    abnormal_flags: Dict[str, int] = {}  # -1, 0, +1 for low/normal/high
    clinical_significance: Dict[str, bool] = {}  # Clinically relevant abnormalities
    
    @model_validator(mode='after') 
    def compute_ml_features(self):
        """Auto-compute derived features for ML"""
        for param, value in self.raw_values.items():
            if value is not None and param in self.reference_ranges:
                low, high = self.reference_ranges[param]
                
                # Z-score normalization
                mid = (low + high) / 2
                std = (high - low) / 4  # Assume 2-sigma range
                self.normalized_values[param] = (value - mid) / std
                
                # Abnormal flags
                if value < low:
                    self.abnormal_flags[param] = -1
                elif value > high:
                    self.abnormal_flags[param] = 1
                else:
                    self.abnormal_flags[param] = 0
                    
                # Clinical significance (customize per lab type)
                self.clinical_significance[param] = abs(self.normalized_values[param]) > 2.0
                
        return self
```

---

## Machine Learning Integration

### Feature Engineering Pipeline
```python
class MLFeatureExtractor:
    """Transform clinical data into ML-ready features"""
    
    def extract_demographic_features(self, demographics: PatientDemographics) -> Dict[str, Any]:
        """Categorical encoding + derived features"""
        features = {}
        
        # One-hot encoding for nominal categories
        for field in ["gender", "education", "occupation"]:
            value = getattr(demographics, field)
            features.update({f"{field}_{value}": 1.0})
            
        # Ordinal encoding for ordered categories  
        age_order = {"18-25": 1, "26-35": 2, "36-45": 3, "46-55": 4, "56-65": 5, "65+": 6}
        features["age_ordinal"] = age_order.get(demographics.age_group, 0)
        
        return features
    
    def extract_symptom_features(self, symptoms: SymptomAssessment) -> Dict[str, Any]:
        """Symptom severity matrix + derived scores"""
        features = {}
        
        # Individual symptom encoding
        for domain, symptom_dict in [
            ("mood", symptoms.mood_symptoms),
            ("psychotic", symptoms.psychotic_symptoms),
            ("anxiety", symptoms.anxiety_symptoms)
        ]:
            for symptom_name, symptom_item in symptom_dict.items():
                # Binary presence
                features[f"{domain}_{symptom_name}_present"] = float(symptom_item.present)
                
                # Severity encoding (0-3 scale)
                if symptom_item.severity:
                    severity_map = {"Mild": 1, "Moderate": 2, "Severe": 3}
                    features[f"{domain}_{symptom_name}_severity"] = severity_map[symptom_item.severity]
                
                # Duration features
                if symptom_item.duration_weeks:
                    features[f"{domain}_{symptom_name}_duration"] = symptom_item.duration_weeks
        
        # Derived domain scores
        features["mood_domain_score"] = sum(1 for s in symptoms.mood_symptoms.values() if s.present)
        features["psychotic_domain_score"] = sum(1 for s in symptoms.psychotic_symptoms.values() if s.present)
        
        return features
```

### Model Training Pipeline
```python
class PsychiatricMLPipeline:
    """End-to-end ML pipeline for psychiatric prediction"""
    
    def __init__(self):
        self.feature_extractor = MLFeatureExtractor()
        self.models = {
            "diagnosis_predictor": RandomForestClassifier(n_estimators=100, random_state=42),
            "severity_predictor": RandomForestRegressor(n_estimators=100, random_state=42),
            "risk_stratifier": LogisticRegression(random_state=42)
        }
        
    def prepare_training_data(self, patient_data: List[Dict]) -> Tuple[np.ndarray, np.ndarray]:
        """Convert clinical records to ML feature matrix"""
        features_list = []
        
        for patient in patient_data:
            features = {}
            features.update(self.feature_extractor.extract_demographic_features(patient['demographics']))
            features.update(self.feature_extractor.extract_symptom_features(patient['symptoms']))
            features.update(self.feature_extractor.extract_history_features(patient['history']))
            
            # Handle lab data if available
            if 'labs' in patient:
                features.update(self.feature_extractor.extract_lab_features(patient['labs']))
            
            features_list.append(features)
        
        # Convert to consistent feature matrix
        feature_df = pd.DataFrame(features_list).fillna(0)
        return feature_df.values, feature_df.columns.tolist()
    
    def train_models(self, X: np.ndarray, y_diagnosis: np.ndarray, y_severity: np.ndarray):
        """Train all models on prepared data"""
        self.models["diagnosis_predictor"].fit(X, y_diagnosis)
        self.models["severity_predictor"].fit(X, y_severity)
        
        # Risk stratification (binary high/low risk)
        y_risk = (y_severity > 7).astype(int)  # Threshold-based risk
        self.models["risk_stratifier"].fit(X, y_risk)
    
    def predict_realtime(self, patient_features: Dict) -> Dict[str, Any]:
        """Real-time prediction during clinical assessment"""
        X = self.feature_extractor.features_to_vector(patient_features)
        
        return {
            "likely_diagnosis": self.models["diagnosis_predictor"].predict(X)[0],
            "severity_score": self.models["severity_predictor"].predict(X)[0],
            "risk_level": "High" if self.models["risk_stratifier"].predict(X)[0] else "Low",
            "confidence_scores": {
                "diagnosis": self.models["diagnosis_predictor"].predict_proba(X)[0].max(),
                "risk": self.models["risk_stratifier"].predict_proba(X)[0].max()
            }
        }
```

---

## API Architecture

### FastAPI Backend Structure
```python
# main.py
from fastapi import FastAPI, Depends, HTTPException, BackgroundTasks
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(
    title="Psychiatric ML Data Collection API",
    version="1.0.0",
    docs_url="/api/docs"  # Keep docs available for development
)

# Serve React frontend
app.mount("/static", StaticFiles(directory="frontend/build/static"), name="static")
app.mount("/", StaticFiles(directory="frontend/build", html=True), name="frontend")

# API routes
@app.post("/api/patients/", response_model=PatientResponse)
async def create_patient(patient: PatientDemographics, db: AsyncSession = Depends(get_db)):
    """Create new patient record with PID validation"""
    
@app.get("/api/patients/{pid}/assessment/", response_model=SymptomAssessment)
async def get_patient_assessment(pid: str, db: AsyncSession = Depends(get_db)):
    """Retrieve patient's latest assessment"""
    
@app.post("/api/ml/predict/", response_model=MLPredictionResponse)  
async def get_ml_prediction(features: PatientFeatures, model: MLPipeline = Depends(get_ml_model)):
    """Real-time ML prediction endpoint"""
    
@app.post("/api/export/training-data/")
async def export_training_data(background_tasks: BackgroundTasks, db: AsyncSession = Depends(get_db)):
    """Generate ML training dataset export"""
```

### Database Layer (SQLAlchemy 2.0)
```python
# database/models.py
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
from sqlalchemy import String, DateTime, JSON, Boolean, Float, Integer

class Base(DeclarativeBase):
    pass

class PatientRecord(Base):
    __tablename__ = "patients"
    
    pid: Mapped[str] = mapped_column(String(24), primary_key=True)
    demographics: Mapped[dict] = mapped_column(JSON)
    symptoms: Mapped[dict] = mapped_column(JSON, nullable=True)
    clinical_history: Mapped[dict] = mapped_column(JSON, nullable=True)
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.now)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.now, onupdate=datetime.now)
    clinician_id: Mapped[str] = mapped_column(String(50))
    is_locked: Mapped[bool] = mapped_column(Boolean, default=False)
    data_version: Mapped[str] = mapped_column(String(10), default="1.0")

class LabResult(Base):
    __tablename__ = "lab_results"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    pid: Mapped[str] = mapped_column(String(24), ForeignKey("patients.pid"))
    lab_data: Mapped[dict] = mapped_column(JSON)
    collection_date: Mapped[datetime] = mapped_column(DateTime)
    
    # Pre-computed ML features for fast access
    ml_features: Mapped[dict] = mapped_column(JSON, nullable=True)
    abnormal_flags: Mapped[dict] = mapped_column(JSON, nullable=True)
```

---

## Security & Privacy Implementation

### PID Management System
```python
class SecurePIDHandler:
    """Military-grade PID security without PII exposure"""
    
    def __init__(self, clinic_secret: str):
        self.clinic_secret = clinic_secret.encode()
    
    def generate_search_token(self, pid: str) -> str:
        """Generate searchable token for database queries"""
        # Double-hashing prevents rainbow table attacks
        first_hash = hmac.new(self.clinic_secret, pid.encode(), hashlib.sha256).digest()
        search_token = hashlib.sha256(first_hash).hexdigest()[:16]
        return f"ST_{search_token}"
    
    def validate_pid_integrity(self, pid: str) -> bool:
        """Validate PID format and checksum without decryption"""
        if not re.match(r'^PID_[A-Za-z0-9+/]{16}[A-Za-z0-9]{4}$', pid):
            return False
        
        # Verify CRC32 checksum
        data_part = pid[4:-4]  # Remove PID_ prefix and checksum suffix
        checksum_part = pid[-4:]
        
        expected_checksum = format(binascii.crc32(data_part.encode()) & 0xffffffff, '08X')[-4:]
        return checksum_part == expected_checksum
```

### Authentication & Session Management
```python
# auth/security.py
from passlib.context import CryptContext
from jose import JWTError, jwt
from datetime import timedelta

pwd_context = CryptContext(schemes=["argon2"], deprecated="auto")

class AuthManager:
    def __init__(self, secret_key: str):
        self.secret_key = secret_key
        self.algorithm = "HS256"
        self.access_token_expire_minutes = 480  # 8 hours
    
    def create_access_token(self, data: dict):
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        to_encode.update({"exp": expire})
        return jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
    
    def verify_token(self, token: str) -> Optional[dict]:
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except JWTError:
            return None
```

---

## Frontend Implementation Guidelines

### React Component Architecture
```typescript
// types/clinical.ts
export interface PatientDemographics {
  pid: string;
  age_group: string;
  gender: string;
  education: string;
  // ... other fields
}

export interface SymptomItem {
  present: boolean;
  severity?: 'Mild' | 'Moderate' | 'Severe';
  duration_weeks?: number;
  functional_impact?: string;
}

export interface MLPrediction {
  likely_diagnosis: string;
  severity_score: number;
  risk_level: 'Low' | 'Medium' | 'High';
  confidence_scores: Record<string, number>;
}
```

### Key UI Components
```typescript
// components/AssessmentWizard.tsx
export const AssessmentWizard: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [patientData, setPatientData] = useState<PatientData>({});
  const [mlPrediction, setMLPrediction] = useState<MLPrediction | null>(null);
  
  // Real-time ML predictions as user fills forms
  const { data: prediction } = useQuery({
    queryKey: ['ml-prediction', patientData],
    queryFn: () => getMlPrediction(patientData),
    enabled: isDataSufficient(patientData),
    refetchInterval: 2000, // Update every 2 seconds
  });
  
  return (
    <div className="assessment-wizard">
      <StepProgress currentStep={currentStep} totalSteps={5} />
      
      {currentStep === 0 && <DemographicsForm data={patientData} onChange={setPatientData} />}
      {currentStep === 1 && <SymptomAssessment data={patientData} onChange={setPatientData} />}
      {currentStep === 2 && <ClinicalHistory data={patientData} onChange={setPatientData} />}
      {currentStep === 3 && <LabDataEntry data={patientData} onChange={setPatientData} />}
      {currentStep === 4 && <ReviewAndSubmit data={patientData} prediction={mlPrediction} />}
      
      {/* Real-time ML insights panel */}
      {prediction && <MLInsightsPanel prediction={prediction} />}
    </div>
  );
};

// components/MLInsightsPanel.tsx  
export const MLInsightsPanel: React.FC<{prediction: MLPrediction}> = ({ prediction }) => {
  return (
    <div className="ml-insights bg-blue-50 p-4 rounded-lg">
      <h3 className="font-bold text-blue-900">AI Clinical Insights</h3>
      
      <div className="grid grid-cols-2 gap-4 mt-3">
        <div>
          <span className="text-sm text-gray-600">Suggested Diagnosis:</span>
          <p className="font-semibold">{prediction.likely_diagnosis}</p>
          <span className="text-xs text-gray-500">
            Confidence: {(prediction.confidence_scores.diagnosis * 100).toFixed(1)}%
          </span>
        </div>
        
        <div>
          <span className="text-sm text-gray-600">Risk Level:</span>
          <p className={`font-semibold ${
            prediction.risk_level === 'High' ? 'text-red-600' : 
            prediction.risk_level === 'Medium' ? 'text-yellow-600' : 'text-green-600'
          }`}>
            {prediction.risk_level}
          </p>
        </div>
      </div>
      
      <div className="mt-4">
        <div className="text-sm text-gray-600 mb-1">Severity Score</div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all"
            style={{ width: `${(prediction.severity_score / 10) * 100}%` }}
          />
        </div>
        <span className="text-xs text-gray-500">
          {prediction.severity_score.toFixed(1)}/10
        </span>
      </div>
    </div>
  );
};
```

---

## Development & Deployment

### Local Development Setup
```bash
# Development environment setup
python -m venv venv
source venv/bin/activate  # or `venv\Scripts\activate` on Windows

pip install -r requirements.txt
npm install  # for frontend dependencies

# Database initialization
alembic upgrade head

# Start development servers
uvicorn main:app --reload --port 8000 &  # Backend API
npm start  # Frontend development server (port 3000)
```

### Production Deployment
```dockerfile
# Dockerfile for containerized deployment
FROM python:3.11-slim

WORKDIR /app

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Build frontend
RUN npm ci && npm run build

# Set production environment
ENV PYTHONPATH=/app
ENV ENVIRONMENT=production

EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "1"]
```

### Configuration Management
```python
# config.py
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    # Application settings
    app_name: str = "Psychiatric ML Data Collection"
    debug: bool = False
    
    # Database
    database_url: str = "sqlite+aiosqlite:///./psychiatric_data.db"
    
    # Security
    secret_key: str
    clinic_secret: str  # For PID generation
    
    # ML Model settings
    model_path: str = "./models/"
    enable_realtime_predictions: bool = True
    
    class Config:
        env_file = ".env"

settings = Settings()
```

This comprehensive specification provides everything needed to build a high-performance, privacy-first psychiatric data collection system with integrated ML capabilities. The architecture supports both training data collection and real-time clinical decision support while maintaining strict privacy controls and optimal performance.

# Database Schema & Data Models - Psychiatric ML System

## SurrealDB Database Design

### Why SurrealDB for This Use Case?
- **Multi-Model Database**: Document, Graph, and Relational in one system
- **Native JSON**: Perfect for complex psychiatric assessment data structures  
- **Advanced Querying**: SQL-like syntax with graph traversal capabilities
- **Embedded Mode**: Single binary, zero-configuration deployment
- **Real-time Features**: Live queries for ML model updates
- **ACID Compliance**: Full transaction support for clinical data integrity
- **Built-in Security**: Row-level permissions, data encryption
- **Performance**: Optimized for complex nested data queries

---

## SurrealDB Schema Definition

### Database Setup
```sql
-- Initialize namespace and database
USE NS psychiatric DB research_data;

-- Define authentication
DEFINE SCOPE clinician_scope SESSION 24h
  SIGNIN (
    SELECT * FROM clinicians WHERE username = $user AND crypto::argon2::compare(password, $pass)
  );
```

### 1. Core Patient Records
```sql
-- Patient demographics and core data
DEFINE TABLE patients SCHEMAFULL
  PERMISSIONS 
    FOR select, update WHERE $scope = "clinician_scope"
    FOR create, delete WHERE $scope = "clinician_scope" AND role CONTAINS "admin";

DEFINE FIELD pid ON patients TYPE string ASSERT $value != NONE;
DEFINE FIELD search_token ON patients TYPE string;

-- Demographics (structured for ML)
DEFINE FIELD demographics ON patients TYPE object;
DEFINE FIELD demographics.age_group ON patients TYPE string 
  ASSERT $value IN ["18-25", "26-35", "36-45", "46-55", "56-65", "65+"];
DEFINE FIELD demographics.gender ON patients TYPE string
  ASSERT $value IN ["Male", "Female", "Non-binary", "Other", "Undisclosed"];
DEFINE FIELD demographics.education ON patients TYPE string
  ASSERT $value IN ["Primary", "Secondary", "Bachelor", "Graduate", "Professional"];
DEFINE FIELD demographics.occupation ON patients TYPE string
  ASSERT $value IN ["Healthcare", "Education", "Technology", "Service", "Manual", "Student", "Unemployed", "Retired", "Other"];
DEFINE FIELD demographics.living_situation ON patients TYPE string;
DEFINE FIELD demographics.referral_source ON patients TYPE string;

-- Assessment data (flexible nested structure)
DEFINE FIELD symptoms ON patients TYPE object DEFAULT {};
DEFINE FIELD clinical_history ON patients TYPE object DEFAULT {};

-- Metadata and audit trail
DEFINE FIELD created_at ON patients TYPE datetime DEFAULT time::now();
DEFINE FIELD updated_at ON patients TYPE datetime DEFAULT time::now();
DEFINE FIELD clinician_id ON patients TYPE string;
DEFINE FIELD is_locked ON patients TYPE bool DEFAULT false;
DEFINE FIELD data_version ON patients TYPE string DEFAULT "1.0";

-- Data quality metrics
DEFINE FIELD completeness_score ON patients TYPE float DEFAULT 0.0;
DEFINE FIELD validation_errors ON patients TYPE array DEFAULT [];

-- ML preprocessing cache (for performance)
DEFINE FIELD ml_features_cache ON patients TYPE object DEFAULT {};
DEFINE FIELD feature_version ON patients TYPE string DEFAULT "1.0";

-- Indexes for fast queries
DEFINE INDEX unique_pid ON patients FIELDS pid UNIQUE;
DEFINE INDEX search_token_idx ON patients FIELDS search_token UNIQUE;
DEFINE INDEX clinician_idx ON patients FIELDS clinician_id;
DEFINE INDEX created_date_idx ON patients FIELDS created_at;
```

### 2. Laboratory Results (Relationship-based)
```sql
DEFINE TABLE lab_results SCHEMAFULL;

DEFINE FIELD id ON lab_results TYPE record<lab_results>;
DEFINE FIELD patient ON lab_results TYPE record<patients>;

-- Lab metadata
DEFINE FIELD lab_type ON lab_results TYPE string
  ASSERT $value IN ["CBC", "Comprehensive_Metabolic", "Liver_Function", "Thyroid", "Lipid_Panel", "Toxicology", "Inflammatory_Markers"];
DEFINE FIELD collection_date ON lab_results TYPE datetime;
DEFINE FIELD lab_facility ON lab_results TYPE string;

-- Raw lab values (nested structure)
DEFINE FIELD raw_values ON lab_results TYPE object;
DEFINE FIELD reference_ranges ON lab_results TYPE object;

-- ML-preprocessed features (computed fields)
DEFINE FIELD normalized_values ON lab_results TYPE object DEFAULT {};
DEFINE FIELD abnormal_flags ON lab_results TYPE object DEFAULT {};
DEFINE FIELD clinical_significance ON lab_results TYPE object DEFAULT {};

-- Derived ratios for clinical interpretation
DEFINE FIELD derived_ratios ON lab_results TYPE object DEFAULT {};
DEFINE FIELD derived_ratios.ast_alt_ratio ON lab_results TYPE float;
DEFINE FIELD derived_ratios.bun_creatinine_ratio ON lab_results TYPE float;
DEFINE FIELD derived_ratios.neutrophil_lymphocyte_ratio ON lab_results TYPE float;

-- Quality metrics
DEFINE FIELD data_quality_score ON lab_results TYPE float DEFAULT 1.0;
DEFINE FIELD validation_notes ON lab_results TYPE string;

DEFINE FIELD created_at ON lab_results TYPE datetime DEFAULT time::now();

-- Relationship definition
DEFINE TABLE has_lab_result SCHEMAFULL;
DEFINE FIELD in ON has_lab_result TYPE record<patients>;
DEFINE FIELD out ON has_lab_result TYPE record<lab_results>;
DEFINE FIELD relationship_created ON has_lab_result TYPE datetime DEFAULT time::now();

-- Indexes for lab queries
DEFINE INDEX lab_patient_idx ON lab_results FIELDS patient;
DEFINE INDEX lab_type_idx ON lab_results FIELDS lab_type;
DEFINE INDEX lab_date_idx ON lab_results FIELDS collection_date;
```

### 3. ML Models and Predictions
```sql
DEFINE TABLE ml_models SCHEMAFULL;

DEFINE FIELD model_id ON ml_models TYPE string;
DEFINE FIELD model_type ON ml_models TYPE string
  ASSERT $value IN ["diagnosis_predictor", "severity_estimator", "risk_stratifier", "outcome_predictor"];
DEFINE FIELD model_version ON ml_models TYPE string;
DEFINE FIELD algorithm ON ml_models TYPE string; -- "RandomForest", "XGBoost", "NeuralNetwork"

-- Model artifacts and metadata  
DEFINE FIELD model_binary ON ml_models TYPE bytes; -- Serialized model
DEFINE FIELD feature_names ON ml_models TYPE array<string>;
DEFINE FIELD hyperparameters ON ml_models TYPE object;
DEFINE FIELD performance_metrics ON ml_models TYPE object;

-- Training metadata
DEFINE FIELD training_date ON ml_models TYPE datetime;
DEFINE FIELD training_data_size ON ml_models TYPE int;
DEFINE FIELD validation_score ON ml_models TYPE float;

DEFINE FIELD is_active ON ml_models TYPE bool DEFAULT false;
DEFINE FIELD created_at ON ml_models TYPE datetime DEFAULT time::now();

-- ML Predictions table
DEFINE TABLE ml_predictions SCHEMAFULL;

DEFINE FIELD prediction_id ON ml_predictions TYPE string;
DEFINE FIELD patient ON ml_predictions TYPE record<patients>;
DEFINE FIELD model_used ON ml_predictions TYPE record<ml_models>;

-- Prediction results
DEFINE FIELD prediction_type ON ml_predictions TYPE string;
DEFINE FIELD prediction_value ON ml_predictions TYPE string; -- Primary prediction
DEFINE FIELD confidence_score ON ml_predictions TYPE float;
DEFINE FIELD probability_distribution ON ml_predictions TYPE object; -- Full class probabilities

-- Supporting data
DEFINE FIELD feature_vector ON ml_predictions TYPE object;
DEFINE FIELD prediction_timestamp ON ml_predictions TYPE datetime DEFAULT time::now();
DEFINE FIELD clinician_feedback ON ml_predictions TYPE string; -- For model improvement

-- Relationship: patient -> predictions
DEFINE TABLE has_prediction SCHEMAFULL;
DEFINE FIELD in ON has_prediction TYPE record<patients>;
DEFINE FIELD out ON has_prediction TYPE record<ml_predictions>;
```

### 4. Symptom Assessment Structure
```sql
DEFINE TABLE symptom_domains SCHEMAFULL;

DEFINE FIELD domain_name ON symptom_domains TYPE string
  ASSERT $value IN ["mood", "psychotic", "anxiety", "cognitive", "behavioral", "somatic"];
DEFINE FIELD domain_description ON symptom_domains TYPE string;
DEFINE FIELD dsm5_reference ON symptom_domains TYPE string;

-- Individual symptoms within domains
DEFINE TABLE symptoms SCHEMAFULL;

DEFINE FIELD symptom_code ON symptoms TYPE string; -- Standardized code
DEFINE FIELD symptom_name ON symptoms TYPE string;
DEFINE FIELD domain ON symptoms TYPE record<symptom_domains>;
DEFINE FIELD dsm5_criteria ON symptoms TYPE string;
DEFINE FIELD icd11_code ON symptoms TYPE string;

-- Patient symptom assessments
DEFINE TABLE patient_symptoms SCHEMAFULL;

DEFINE FIELD patient ON patient_symptoms TYPE record<patients>;
DEFINE FIELD symptom ON patient_symptoms TYPE record<symptoms>;
DEFINE FIELD assessment_date ON patient_symptoms TYPE datetime;

-- Symptom details
DEFINE FIELD present ON patient_symptoms TYPE bool;
DEFINE FIELD severity ON patient_symptoms TYPE string
  ASSERT $value IN ["Mild", "Moderate", "Severe"] OR $value = NONE;
DEFINE FIELD duration_weeks ON patient_symptoms TYPE int;
DEFINE FIELD functional_impact ON patient_symptoms TYPE string
  ASSERT $value IN ["None", "Mild", "Moderate", "Severe"] OR $value = NONE;
DEFINE FIELD onset_pattern ON patient_symptoms TYPE string;
DEFINE FIELD triggers ON patient_symptoms TYPE array<string>;

DEFINE FIELD clinician_notes ON patient_symptoms TYPE string;
DEFINE FIELD created_at ON patient_symptoms TYPE datetime DEFAULT time::now();
```

---

## Advanced SurrealDB Queries for ML Pipeline

### 1. Patient Data Extraction for ML
```sql
-- Get complete patient feature set for ML training
SELECT 
  pid,
  demographics,
  {
    mood_symptoms: (SELECT symptom.symptom_code, present, severity, duration_weeks 
                   FROM patient_symptoms WHERE patient = $parent.id 
                   AND symptom.domain.domain_name = "mood"),
    anxiety_symptoms: (SELECT symptom.symptom_code, present, severity, duration_weeks 
                      FROM patient_symptoms WHERE patient = $parent.id 
                      AND symptom.domain.domain_name = "anxiety"),
    psychotic_symptoms: (SELECT symptom.symptom_code, present, severity, duration_weeks 
                        FROM patient_symptoms WHERE patient = $parent.id 
                        AND symptom.domain.domain_name = "psychotic")
  } AS symptom_profile,
  clinical_history,
  (SELECT * FROM lab_results WHERE patient = $parent.id ORDER BY collection_date DESC LIMIT 5) AS recent_labs,
  completeness_score
FROM patients 
WHERE is_locked = true AND completeness_score > 0.8;
```

### 2. Real-time Feature Engineering
```sql
-- Compute ML features with derived metrics
SELECT 
  pid,
  demographics.age_group AS age_group,
  demographics.gender AS gender,
  
  -- Symptom domain scores
  array::len(SELECT * FROM patient_symptoms WHERE patient = $parent.id AND present = true AND symptom.domain.domain_name = "mood") AS mood_symptom_count,
  array::len(SELECT * FROM patient_symptoms WHERE patient = $parent.id AND severity = "Severe") AS severe_symptom_count,
  
  -- Lab abnormalities
  (SELECT 
    math::sum(object::values(abnormal_flags)) AS total_abnormal_labs,
    array::len(object::keys(clinical_significance)) AS significant_findings
   FROM lab_results WHERE patient = $parent.id ORDER BY collection_date DESC LIMIT 1)[0] AS lab_summary,
   
  -- Risk factors
  clinical_history.suicide_attempts AS suicide_history,
  clinical_history.hospitalization_count AS hospitalization_count,
  
  -- Temporal features
  time::format(created_at, "%Y-%m") AS assessment_month
FROM patients;
```

### 3. ML Model Performance Tracking
```sql
-- Track prediction accuracy over time
SELECT 
  model_used.model_type,
  model_used.model_version,
  confidence_score,
  clinician_feedback,
  prediction_timestamp
FROM ml_predictions 
WHERE clinician_feedback IS NOT NONE
ORDER BY prediction_timestamp DESC
LIMIT 1000;
```

### 4. Data Quality Monitoring
```sql
-- Identify data quality issues across the dataset
SELECT 
  demographics.age_group,
  count() AS patient_count,
  math::mean(completeness_score) AS avg_completeness,
  array::len(SELECT * FROM patients WHERE array::len(validation_errors) > 0) AS error_count,
  
  -- Lab data coverage
  (SELECT count() FROM lab_results WHERE patient.demographics.age_group = $parent.demographics.age_group) AS total_labs
FROM patients
GROUP BY demographics.age_group
ORDER BY avg_completeness DESC;
```

---

## SurrealDB Python Integration

### Database Connection Setup
```python
from surrealdb import Surreal

class SurrealDBManager:
    def __init__(self, database_path: str = "file://psychiatric_data.db"):
        self.db_path = database_path
        self.db = None
    
    async def connect(self):
        """Initialize SurrealDB connection"""
        self.db = Surreal()
        await self.db.connect(self.db_path)
        await self.db.use("psychiatric", "research_data")
    
    async def create_patient(self, patient_data: dict) -> str:
        """Create new patient record with validation"""
        result = await self.db.create("patients", {
            "pid": patient_data["pid"],
            "search_token": self.generate_search_token(patient_data["pid"]),
            "demographics": patient_data["demographics"],
            "clinician_id": patient_data["clinician_id"]
        })
        return result[0]["id"]
    
    async def get_patient_ml_features(self, pid: str) -> dict:
        """Extract ML-ready features for a patient"""
        query = """
        SELECT 
          demographics,
          (SELECT * FROM patient_symptoms WHERE patient.pid = $pid) AS symptoms,
          clinical_history,
          (SELECT * FROM lab_results WHERE patient.pid = $pid ORDER BY collection_date DESC LIMIT 3) AS recent_labs
        FROM patients 
        WHERE pid = $pid
        """
        result = await self.db.query(query, {"pid": pid})
        return result[0] if result else None
    
    async def store_ml_prediction(self, prediction_data: dict) -> str:
        """Store ML prediction results"""
        result = await self.db.create("ml_predictions", prediction_data)
        return result[0]["id"]
```

### Real-time Query Subscriptions
```python
async def setup_realtime_ml_updates():
    """Setup live query for ML model updates"""
    
    # Live query for new patient data
    async def handle_new_patient(message):
        patient_data = message["result"]
        # Trigger ML feature extraction
        features = await extract_ml_features(patient_data)
        # Update ML cache
        await update_ml_cache(patient_data["pid"], features)
    
    await db.live_query("SELECT * FROM patients WHERE is_locked = false", handle_new_patient)
```

This SurrealDB schema provides a much more sophisticated and flexible foundation for the psychiatric ML application, with native support for complex nested data, graph relationships, and real-time capabilities that will be essential for ML model integration.

# ML Pipeline & Real-time Inference Guide

## ML Architecture Overview

This guide covers building a complete ML pipeline that operates in two phases:
1. **Training Phase**: Collect and process clinical data for model development
2. **Inference Phase**: Real-time predictions during patient assessments

### Core ML Pipeline Components
```
Data Collection → Feature Engineering → Model Training → Model Validation → Deployment → Real-time Inference
```

---

## Feature Engineering Pipeline

### 1. Demographic Features Encoder
```python
import pandas as pd
import numpy as np
from sklearn.preprocessing import LabelEncoder, OneHotEncoder, StandardScaler
from typing import Dict, List, Any

class DemographicFeatureEncoder:
    """Transform demographic data into ML-ready features"""
    
    def __init__(self):
        self.encoders = {}
        self.feature_names = []
    
    def fit_transform(self, demographic_data: List[Dict]) -> np.ndarray:
        """Fit encoders and transform demographic data"""
        df = pd.DataFrame(demographic_data)
        
        # One-hot encoding for nominal categories
        nominal_features = ['gender', 'occupation', 'living_situation', 'referral_source']
        for feature in nominal_features:
            if feature in df.columns:
                encoder = OneHotEncoder(sparse_output=False, handle_unknown='ignore')
                encoded = encoder.fit_transform(df[[feature]])
                
                # Store encoder and feature names
                self.encoders[f"{feature}_onehot"] = encoder
                feature_names = [f"{feature}_{cat}" for cat in encoder.categories_[0]]
                self.feature_names.extend(feature_names)
        
        # Ordinal encoding for ordered categories
        ordinal_mappings = {
            'age_group': {'18-25': 1, '26-35': 2, '36-45': 3, '46-55': 4, '56-65': 5, '65+': 6},
            'education': {'Primary': 1, 'Secondary': 2, 'Bachelor': 3, 'Graduate': 4, 'Professional': 5}
        }
        
        for feature, mapping in ordinal_mappings.items():
            if feature in df.columns:
                df[f"{feature}_ordinal"] = df[feature].map(mapping)
                self.feature_names.append(f"{feature}_ordinal")
        
        return self._combine_features(df)
    
    def transform(self, demographic_data: Dict) -> np.ndarray:
        """Transform new demographic data using fitted encoders"""
        # Implementation for real-time inference
        pass

class SymptomFeatureEncoder:
    """Extract features from psychiatric symptom assessments"""
    
    def __init__(self):
        self.symptom_domains = ['mood', 'psychotic', 'anxiety', 'cognitive', 'behavioral']
        self.severity_mapping = {'Mild': 1, 'Moderate': 2, 'Severe': 3}
    
    def extract_features(self, symptom_data: Dict) -> Dict[str, float]:
        """Extract comprehensive symptom features"""
        features = {}
        
        for domain in self.symptom_domains:
            domain_symptoms = symptom_data.get(f"{domain}_symptoms", {})
            
            # Basic counts
            features[f"{domain}_symptom_count"] = sum(1 for s in domain_symptoms.values() if s.get('present', False))
            
            # Severity metrics
            severities = [self.severity_mapping.get(s.get('severity'), 0) 
                         for s in domain_symptoms.values() if s.get('present', False)]
            
            if severities:
                features[f"{domain}_max_severity"] = max(severities)
                features[f"{domain}_avg_severity"] = np.mean(severities)
                features[f"{domain}_severe_count"] = sum(1 for s in severities if s == 3)
            else:
                features[f"{domain}_max_severity"] = 0
                features[f"{domain}_avg_severity"] = 0
                features[f"{domain}_severe_count"] = 0
            
            # Duration features
            durations = [s.get('duration_weeks', 0) 
                        for s in domain_symptoms.values() if s.get('present', False)]
            
            if durations:
                features[f"{domain}_max_duration"] = max(durations)
                features[f"{domain}_avg_duration"] = np.mean(durations)
                features[f"{domain}_chronic_count"] = sum(1 for d in durations if d > 26)  # >6 months
            else:
                features[f"{domain}_max_duration"] = 0
                features[f"{domain}_avg_duration"] = 0
                features[f"{domain}_chronic_count"] = 0
        
        # Cross-domain features
        total_symptoms = sum(features[f"{domain}_symptom_count"] for domain in self.symptom_domains)
        features['total_symptom_count'] = total_symptoms
        features['symptom_domain_breadth'] = sum(1 for domain in self.symptom_domains 
                                               if features[f"{domain}_symptom_count"] > 0)
        
        # Severity index (weighted composite score)
        severity_weights = {'mood': 0.3, 'psychotic': 0.25, 'anxiety': 0.2, 'cognitive': 0.15, 'behavioral': 0.1}
        weighted_severity = sum(severity_weights[domain] * features[f"{domain}_avg_severity"] 
                               for domain in self.symptom_domains)
        features['composite_severity_index'] = weighted_severity
        
        return features

class LabFeatureEncoder:
    """Process laboratory data for ML features"""
    
    def __init__(self):
        self.lab_parameters = {
            'CBC': ['WBC', 'RBC', 'Hemoglobin', 'Hematocrit', 'Platelets', 'Neutrophils_%', 'Lymphocytes_%'],
            'Metabolic': ['Glucose', 'BUN', 'Creatinine', 'eGFR', 'Sodium', 'Potassium', 'Chloride'],
            'Liver': ['AST', 'ALT', 'ALP', 'Total_Bilirubin', 'Albumin'],
            'Thyroid': ['TSH', 'Free_T4', 'Free_T3']
        }
        self.reference_ranges = self._load_reference_ranges()
    
    def extract_features(self, lab_data: List[Dict]) -> Dict[str, float]:
        """Extract ML features from lab results"""
        if not lab_data:
            return self._get_missing_lab_features()
        
        # Use most recent labs of each type
        latest_labs = {}
        for lab in lab_data:
            lab_type = lab['lab_type']
            if lab_type not in latest_labs or lab['collection_date'] > latest_labs[lab_type]['collection_date']:
                latest_labs[lab_type] = lab
        
        features = {}
        
        for lab_type, lab_result in latest_labs.items():
            raw_values = lab_result.get('raw_values', {})
            
            # Raw value features (normalized)
            for param, value in raw_values.items():
                if value is not None:
                    # Z-score normalization
                    ref_range = self.reference_ranges.get(param)
                    if ref_range:
                        low, high = ref_range
                        mid = (low + high) / 2
                        std = (high - low) / 4  # Assume 2-sigma reference range
                        z_score = (value - mid) / std
                        features[f"{param}_zscore"] = z_score
                        
                        # Abnormality flags
                        if value < low:
                            features[f"{param}_abnormal"] = -1
                        elif value > high:
                            features[f"{param}_abnormal"] = 1
                        else:
                            features[f"{param}_abnormal"] = 0
            
            # Derived ratio features (clinically significant)
            if lab_type == 'Liver' and 'AST' in raw_values and 'ALT' in raw_values:
                if raw_values['AST'] and raw_values['ALT']:
                    features['AST_ALT_ratio'] = raw_values['AST'] / raw_values['ALT']
            
            if lab_type == 'Metabolic' and 'BUN' in raw_values and 'Creatinine' in raw_values:
                if raw_values['BUN'] and raw_values['Creatinine']:
                    features['BUN_Creatinine_ratio'] = raw_values['BUN'] / raw_values['Creatinine']
            
            if lab_type == 'CBC' and 'Neutrophils_%' in raw_values and 'Lymphocytes_%' in raw_values:
                if raw_values['Neutrophils_%'] and raw_values['Lymphocytes_%']:
                    features['Neutrophil_Lymphocyte_ratio'] = raw_values['Neutrophils_%'] / raw_values['Lymphocytes_%']
        
        # Aggregate abnormality metrics
        abnormal_counts = [v for k, v in features.items() if k.endswith('_abnormal') and v != 0]
        features['total_abnormal_labs'] = len(abnormal_counts)
        features['abnormal_lab_severity'] = sum(abs(v) for v in abnormal_counts)
        
        return features
    
    def _load_reference_ranges(self) -> Dict[str, tuple]:
        """Load standard reference ranges for lab parameters"""
        return {
            'WBC': (4.0, 11.0),
            'RBC': (4.2, 5.4),
            'Hemoglobin': (12.0, 16.0),
            'Hematocrit': (36.0, 48.0),
            'Platelets': (150, 450),
            'Glucose': (70, 100),
            'BUN': (7, 20),
            'Creatinine': (0.6, 1.2),
            'AST': (10, 40),
            'ALT': (7, 35),
            'TSH': (0.4, 4.0),
            # Add more reference ranges as needed
        }
```

---

## ML Model Development

### 1. Multi-Task Learning Architecture
```python
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import cross_val_score, GridSearchCV
from sklearn.metrics import classification_report, mean_squared_error, roc_auc_score
import joblib

class PsychiatricMLPipeline:
    """Multi-task ML pipeline for psychiatric predictions"""
    
    def __init__(self):
        self.models = {}
        self.feature_encoders = {
            'demographics': DemographicFeatureEncoder(),
            'symptoms': SymptomFeatureEncoder(),
            'labs': LabFeatureEncoder()
        }
        self.feature_names = []
        self.is_trained = False
    
    async def prepare_training_data(self, db_manager) -> tuple:
        """Extract and prepare training data from SurrealDB"""
        
        # Query complete patient records
        query = """
        SELECT 
          pid,
          demographics,
          clinical_history,
          (SELECT * FROM patient_symptoms WHERE patient = $parent.id) AS symptoms,
          (SELECT * FROM lab_results WHERE patient = $parent.id 
           ORDER BY collection_date DESC LIMIT 3) AS recent_labs
        FROM patients 
        WHERE is_locked = true AND completeness_score > 0.7
        """
        
        patient_records = await db_manager.db.query(query)
        
        # Feature extraction
        X_features = []
        y_diagnosis = []
        y_severity = []
        
        for record in patient_records:
            # Extract features from each data source
            demo_features = self.feature_encoders['demographics'].extract_features(record['demographics'])
            symptom_features = self.feature_encoders['symptoms'].extract_features(record['symptoms'])
            lab_features = self.feature_encoders

# Complete Implementation Guide - Psychiatric ML Data Collection System

## Project Structure & Setup

### 1. Project Directory Structure
```
psychiatric_ml_system/
├── backend/
│   ├── main.py                 # FastAPI application entry point
│   ├── config.py              # Configuration management
│   ├── database/
│   │   ├── __init__.py
│   │   ├── connection.py      # SurrealDB connection manager
│   │   ├── models.py         # Pydantic data models
│   │   └── schema.sql        # SurrealDB schema definitions
│   ├── ml/
│   │   ├── __init__.py
│   │   ├── pipeline.py       # ML pipeline implementation
│   │   ├── features.py       # Feature engineering
│   │   └── monitoring.py     # Model performance monitoring
│   ├── api/
│   │   ├── __init__.py
│   │   ├── auth.py          # Authentication endpoints
│   │   ├── patients.py      # Patient data endpoints
│   │   ├── ml_predictions.py # ML prediction endpoints
│   │   └── exports.py       # Data export endpoints
│   ├── security/
│   │   ├── __init__.py
│   │   ├── pid_manager.py   # PID encryption/validation
│   │   └── auth_manager.py  # JWT and session management
│   └── utils/
│       ├── __init__.py
│       ├── logging.py       # Structured logging
│       └── validators.py    # Data validation utilities
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   │   ├── AssessmentWizard.tsx
│   │   │   ├── MLInsightsPanel.tsx
│   │   │   ├── PatientSearch.tsx
│   │   │   └── DataExport.tsx
│   │   ├── types/
│   │   │   ├── clinical.ts
│   │   │   ├── ml.ts
│   │   │   └── api.ts
│   │   ├── services/
│   │   │   ├── api.ts
│   │   │   └── auth.ts
│   │   ├── hooks/
│   │   │   ├── usePatientData.ts
│   │   │   ├── useMLPredictions.ts
│   │   │   └── useAuth.ts
│   │   └── App.tsx
│   ├── package.json
│   └── tailwind.config.js
├── models/                    # Trained ML models storage
├── data/                     # Database files and exports
├── configs/
│   ├── development.env
│   ├── production.env
│   └── schema_init.sql
├── tests/
│   ├── test_ml_pipeline.py
│   ├── test_api_endpoints.py
│   └── test_security.py
├── scripts/
│   ├── init_database.py
│   ├── train_models.py
│   └── export_data.py
├── requirements.txt
├── package.json
├── Dockerfile
├── docker-compose.yml
└── README.md
```

---

## Installation & Setup Instructions

### 1. Development Environment Setup

```bash
# Clone repository (or create new project)
git clone <repository-url>
cd psychiatric_ml_system

# Create Python virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install Python dependencies
pip install -r requirements.txt

# Install Node.js dependencies for frontend
cd frontend
npm install
cd ..

# Install SurrealDB
# Option 1: Download binary
curl -sSf https://install.surrealdb.com | sh

# Option 2: Using package manager
# macOS: brew install surrealdb/tap/surreal
# Windows: scoop install surrealdb

# Initialize database schema
python scripts/init_database.py

# Create configuration files
cp configs/development.env .env
# Edit .env with your specific settings
```

### 2. Configuration Files

#### requirements.txt
```txt
# Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database
surrealdb==0.3.2
asyncio==3.4.3

# Data Processing & ML
pandas==2.1.3
numpy==1.25.2
scikit-learn==1.3.2
joblib==1.3.2
pydantic==2.5.0

# Security & Authentication
passlib[argon2]==1.7.4
python-jose[cryptography]==3.3.0
cryptography==41.0.7

# Utilities
python-dotenv==1.0.0
loguru==0.7.2
rich==13.7.0
typer==0.9.0

# Development & Testing
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
mypy==1.7.1
```

#### .env (Development Configuration)
```env
# Application Settings
APP_NAME="Psychiatric ML Data Collection"
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# Database Configuration
SURREALDB_URL=memory://
SURREALDB_NAMESPACE=psychiatric
SURREALDB_DATABASE=research_data
SURREALDB_USERNAME=root
SURREALDB_PASSWORD=development_password

# Security Configuration
SECRET_KEY=your-super-secret-key-here-change-in-production
CLINIC_SECRET=clinic-specific-encryption-key
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=480

# ML Configuration
ML_MODELS_PATH=./models
ENABLE_REALTIME_PREDICTIONS=true
MIN_TRAINING_SAMPLES=100
MODEL_RETRAIN_THRESHOLD=0.7

# API Configuration
API_V1_PREFIX=/api/v1
CORS_ORIGINS=["http://localhost:3000"]
```

---

## Core Implementation Files



## Scripts

### scripts/init_database.py
```python
import asyncio
import sys
import os
from pathlib import Path

# Add the backend directory to the path
sys.path.append(str(Path(__file__).parent.parent / "backend"))

from database.connection import SurrealDBManager
from config import settings
from utils.logging import setup_logging

async def init_database():
    """Initialize the database with schema and sample data"""
    logger = setup_logging()
    
    # Connect to database
    db_manager = SurrealDBManager(settings.surrealdb_url)
    await db_manager.connect()
    
    logger.info("Database initialized successfully")
    
    # Create sample clinician user (in a real app, this would be handled by a user management system)
    try:
        # This is a simplified example - in production, use proper authentication
        logger.info("Database setup complete")
    except Exception as e:
        logger.error(f"Error during database initialization: {e}")
    
    await db_manager.disconnect()

if __name__ == "__main__":
    asyncio.run(init_database())
```

### scripts/train_models.py
```python
import asyncio
import sys
import os
from pathlib import Path

# Add the backend directory to the path
sys.path.append(str(Path(__file__).parent.parent / "backend"))

from database.connection import SurrealDBManager
from ml.pipeline import PsychiatricMLPipeline
from config import settings
from utils.logging import setup_logging

async def train_models():
    """Train ML models with existing data"""
    logger = setup_logging()
    
    # Connect to database
    db_manager = SurrealDBManager(settings.surrealdb_url)
    await db_manager.connect()
    
    # Initialize ML pipeline
    ml_pipeline = PsychiatricMLPipeline(settings.ml_models_path)
    
    try:
        # Prepare training data
        logger.info("Preparing training data...")
        X, labels = await ml_pipeline.prepare_training_data(db_manager)
        
        if len(X) < settings.min_training_samples:
            logger.warning(f"Insufficient training data: {len(X)} samples (minimum {settings.min_training_samples} required)")
            return
        
        # Train models
        logger.info("Training ML models...")
        performance = ml_pipeline.train_models(X, labels)
        
        # Save models
        logger.info("Saving trained models...")
        model_path = ml_pipeline.save_models()
        
        logger.info(f"Model training completed successfully!")
        logger.info(f"Models saved to: {model_path}")
        logger.info(f"Performance metrics: {performance}")
        
    except Exception as e:
        logger.error(f"Error during model training: {e}")
    
    finally:
        await db_manager.disconnect()

if __name__ == "__main__":
    asyncio.run(train_models())
```

### scripts/export_data.py
```python
import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime

# Add the backend directory to the path
sys.path.append(str(Path(__file__).parent.parent / "backend"))

from database.connection import SurrealDBManager
from config import settings
from utils.logging import setup_logging
import pandas as pd
import json

async def export_data():
    """Export patient data for analysis or model training"""
    logger = setup_logging()
    
    # Connect to database
    db_manager = SurrealDBManager(settings.surrealdb_url)
    await db_manager.connect()
    
    try:
        # Get all patient data
        logger.info("Retrieving patient data...")
        patient_data = await db_manager.get_ml_training_data()
        
        if not patient_data:
            logger.warning("No patient data found to export")
            return
        
        # Create export directory if it doesn't exist
        export_dir = Path("./data/exports")
        export_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Export as JSON
        json_path = export_dir / f"patient_data_{timestamp}.json"
        with open(json_path, 'w') as f:
            json.dump(patient_data, f, indent=2, default=str)
        
        # Export as CSV (flattened)
        df = pd.json_normalize(patient_data)
        csv_path = export_dir / f"patient_data_{timestamp}.csv"
        df.to_csv(csv_path, index=False)
        
        logger.info(f"Data export completed successfully!")
        logger.info(f"JSON export: {json_path}")
        logger.info(f"CSV export: {csv_path}")
        logger.info(f"Exported {len(patient_data)} patient records")
        
    except Exception as e:
        logger.error(f"Error during data export: {e}")
    
    finally:
        await db_manager.disconnect()

if __name__ == "__main__":
    asyncio.run(export_data())
```