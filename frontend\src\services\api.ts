import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { 
  AuthToken, 
  User, 
  Patient, 
  PatientDemographics, 
  SymptomAssessment, 
  ClinicalHistory,
  PatientSummary,
  MLPrediction,
  MLModelStatus,
  ExportRequest,
  ApiResponse 
} from '../types';

class ApiService {
  private api: AxiosInstance;
  private token: string | null = null;

  constructor() {
    this.api = axios.create({
      baseURL: process.env.REACT_APP_API_URL || '/api/v1',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        if (this.token) {
          config.headers.Authorization = `Bearer ${this.token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          this.clearToken();
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );

    // Load token from localStorage
    this.loadToken();
  }

  private loadToken(): void {
    const savedToken = localStorage.getItem('auth_token');
    if (savedToken) {
      this.token = savedToken;
    }
  }

  private saveToken(token: string): void {
    this.token = token;
    localStorage.setItem('auth_token', token);
  }

  private clearToken(): void {
    this.token = null;
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_info');
  }

  // Authentication methods
  async login(username: string, password: string): Promise<AuthToken> {
    const formData = new FormData();
    formData.append('username', username);
    formData.append('password', password);

    const response: AxiosResponse<AuthToken> = await this.api.post('/auth/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });

    this.saveToken(response.data.access_token);
    localStorage.setItem('user_info', JSON.stringify(response.data.user_info));
    
    return response.data;
  }

  async logout(): Promise<void> {
    try {
      await this.api.post('/auth/logout');
    } finally {
      this.clearToken();
    }
  }

  async getCurrentUser(): Promise<User> {
    const response: AxiosResponse<User> = await this.api.get('/auth/me');
    return response.data;
  }

  async validateToken(): Promise<boolean> {
    try {
      await this.api.get('/auth/validate');
      return true;
    } catch {
      return false;
    }
  }

  // Patient management methods
  async createPatient(originalPatientId: string, demographics: PatientDemographics, clinicKey?: string): Promise<{ pid: string; patient_id: string; message: string }> {
    const response = await this.api.post('/patients/create', {
      original_patient_id: originalPatientId,
      demographics,
      clinic_key: clinicKey,
    });
    return response.data;
  }

  async getPatient(pid: string): Promise<Patient> {
    const response: AxiosResponse<Patient> = await this.api.get(`/patients/${pid}`);
    return response.data;
  }

  async updatePatientSymptoms(pid: string, symptoms: SymptomAssessment): Promise<{ message: string }> {
    const response = await this.api.put(`/patients/${pid}/symptoms`, {
      pid,
      symptoms,
    });
    return response.data;
  }

  async updatePatientHistory(pid: string, clinicalHistory: ClinicalHistory): Promise<{ message: string }> {
    const response = await this.api.put(`/patients/${pid}/history`, {
      pid,
      clinical_history: clinicalHistory,
    });
    return response.data;
  }

  async searchPatients(filters?: {
    date_from?: string;
    date_to?: string;
  }): Promise<{ patients: PatientSummary[]; total: number }> {
    const params = new URLSearchParams();
    if (filters?.date_from) params.append('date_from', filters.date_from);
    if (filters?.date_to) params.append('date_to', filters.date_to);

    const response = await this.api.get(`/patients/search?${params.toString()}`);
    return response.data;
  }

  // ML prediction methods
  async predictPatientOutcomes(pid: string): Promise<{
    pid: string;
    predictions: MLPrediction;
    timestamp: string;
    data_completeness: number;
  }> {
    const response = await this.api.post('/ml/predict', { pid });
    return response.data;
  }

  async trainModels(forceRetrain: boolean = false): Promise<{
    status: string;
    message: string;
    performance_metrics?: Record<string, number>;
    training_samples?: number;
  }> {
    const response = await this.api.post('/ml/train', {
      force_retrain: forceRetrain,
    });
    return response.data;
  }

  async getModelStatus(): Promise<MLModelStatus> {
    const response: AxiosResponse<MLModelStatus> = await this.api.get('/ml/status');
    return response.data;
  }

  async submitPredictionFeedback(predictionId: string, feedback: string, correctDiagnosis?: string): Promise<{ message: string }> {
    const response = await this.api.post('/ml/feedback', {
      prediction_id: predictionId,
      feedback,
      correct_diagnosis: correctDiagnosis,
    });
    return response.data;
  }

  // Data export methods
  async requestDataExport(exportRequest: ExportRequest): Promise<Blob> {
    const response = await this.api.post('/exports/request', exportRequest, {
      responseType: 'blob',
    });
    return response.data;
  }

  async getExportFormats(): Promise<{
    formats: Record<string, {
      name: string;
      description: string;
      file_extension: string;
    }>;
  }> {
    const response = await this.api.get('/exports/formats');
    return response.data;
  }

  // Health check
  async healthCheck(): Promise<{
    status: string;
    timestamp: string;
    version: string;
    environment: string;
    database: string;
    ml_models: string;
  }> {
    const response = await this.api.get('/health');
    return response.data;
  }

  // Utility methods
  isAuthenticated(): boolean {
    return !!this.token;
  }

  getCurrentUserInfo(): User | null {
    const userInfo = localStorage.getItem('user_info');
    return userInfo ? JSON.parse(userInfo) : null;
  }

  // Error handling helper
  handleApiError(error: any): string {
    if (error.response?.data?.detail) {
      return error.response.data.detail;
    }
    if (error.message) {
      return error.message;
    }
    return 'An unexpected error occurred';
  }
}

// Create and export a singleton instance
export const apiService = new ApiService();
export default apiService;
