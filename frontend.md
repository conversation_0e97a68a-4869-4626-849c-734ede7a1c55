## Frontend Implementation

### frontend/src/types/clinical.ts
```typescript
export interface PatientDemographics {
  pid: string;
  age_group: "18-25" | "26-35" | "36-45" | "46-55" | "56-65" | "65+";
  gender: "Male" | "Female" | "Non-binary" | "Other" | "Undisclosed";
  education: "Primary" | "Secondary" | "Bachelor" | "Graduate" | "Professional";
  occupation: "Healthcare" | "Education" | "Technology" | "Service" | "Manual" | "Student" | "Unemployed" | "Retired" | "Other";
  marital_status: "Single" | "Married" | "Divorced" | "Widowed" | "Separated";
  living_situation: "Alone" | "Family" | "Shared" | "Assisted" | "Institutional";
  referral_source: "Self" | "GP" | "Specialist" | "Emergency" | "Court" | "Other";
  insurance_type: "Private" | "Public" | "Uninsured" | "Unknown";
  created_at?: string;
  clinician_id: string;
  data_quality_score?: number;
}

export interface SymptomItem {
  present: boolean;
  severity?: "Mild" | "Moderate" | "Severe";
  duration_weeks?: number;
  functional_impact?: "None" | "Mild" | "Moderate" | "Severe";
}

export interface SymptomAssessment {
  pid: string;
  assessment_date?: string;
  mood_symptoms: Record<string, SymptomItem>;
  psychotic_symptoms: Record<string, SymptomItem>;
  anxiety_symptoms: Record<string, SymptomItem>;
  cognitive_symptoms: Record<string, SymptomItem>;
  behavioral_symptoms: Record<string, SymptomItem>;
  functioning_level: number;
  symptom_severity_index: number;
}

export interface SubstanceUseItem {
  current_use: boolean;
  frequency?: "Daily" | "Weekly" | "Monthly" | "Occasional";
  severity?: "Mild" | "Moderate" | "Severe";
  years_of_use?: number;
}

export interface ClinicalHistory {
  pid: string;
  previous_diagnoses: string[];
  hospitalization_count: number;
  suicide_attempts: number;
  medication_trials: Record<string, string>;
  therapy_history: string[];
  family_psychiatric_history: boolean;
  family_conditions: string[];
  substance_use: Record<string, SubstanceUseItem>;
  medical_conditions: string[];
  current_medications: string[];
  trauma_history: boolean;
  social_support_level: "High" | "Moderate" | "Low" | "Minimal";
  housing_stability: "Stable" | "Temporary" | "Unstable" | "Homeless";
}

export interface LabResults {
  pid: string;
  collection_date: string;
  lab_type: "Basic_Metabolic" | "CBC" | "Liver_Function" | "Thyroid" | "Toxicology" | "Inflammatory_Markers";
  raw_values: Record<string, number | null>;
  reference_ranges: Record<string, [number, number]>;
  normalized_values?: Record<string, number | null>;
  abnormal_flags?: Record<string, number>;
  clinical_significance?: Record<string, boolean>;
}
```

### frontend/src/types/ml.ts
```typescript
export interface MLPrediction {
  likely_diagnosis: string;
  severity_score: number;
  risk_level: "Low" | "Medium" | "High";
  confidence_scores: {
    diagnosis: number;
    risk: number;
  };
  influential_features: Array<{
    feature: string;
    importance: number;
    value: number;
  }>;
  prediction_timestamp: string;
  model_version: string;
}

export interface ModelInfo {
  is_trained: boolean;
  feature_count: number;
  performance_metrics: Record<string, number>;
  training_metadata: {
    training_samples: number;
    test_samples: number;
    feature_count: number;
    diagnosis_classes: string[];
    training_date: string;
  };
  model_types: string[];
}

export interface PatientFeatures {
  demographics?: PatientDemographics;
  symptoms?: SymptomAssessment;
  clinical_history?: ClinicalHistory;
  labs?: LabResults[];
}
```

### frontend/src/types/api.ts
```typescript
import { PatientDemographics, SymptomAssessment, ClinicalHistory, LabResults } from './clinical';
import { MLPrediction, ModelInfo } from './ml';

export interface PatientResponse {
  id: string;
  pid: string;
  demographics: Record<string, any>;
  symptoms?: Record<string, any>;
  clinical_history?: Record<string, any>;
  created_at: string;
  updated_at: string;
  is_locked: boolean;
  completeness_score: number;
}

export interface AuthResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
}

export interface User {
  username: string;
  user_id: string;
  roles: string[];
}

export interface ExportStatus {
  status: "processing" | "completed";
  filename: string;
  size_bytes?: number;
  created_at?: string;
  message?: string;
}
```

### frontend/src/services/api.ts
```typescript
import { PatientDemographics, SymptomAssessment, ClinicalHistory, LabResults } from '../types/clinical';
import { MLPrediction, ModelInfo, PatientFeatures } from '../types/ml';
import { PatientResponse, AuthResponse, User, ExportStatus } from '../types/api';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1';

class ApiService {
  private getToken(): string | null {
    return localStorage.getItem('access_token');
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const token = this.getToken();
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    };

    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `API Error: ${response.status}`);
    }

    return response.json();
  }

  // Authentication
  async login(username: string, password: string): Promise<AuthResponse> {
    const formData = new FormData();
    formData.append('username', username);
    formData.append('password', password);

    return this.request<AuthResponse>('/auth/login', {
      method: 'POST',
      body: formData,
      headers: {}, // Let browser set Content-Type for FormData
    });
  }

  async getCurrentUser(): Promise<User> {
    return this.request<User>('/auth/me');
  }

  // Patients
  async createPatient(patientData: {
    pid: string;
    demographics: PatientDemographics;
    clinician_id: string;
  }): Promise<PatientResponse> {
    return this.request<PatientResponse>('/patients/', {
      method: 'POST',
      body: JSON.stringify(patientData),
    });
  }

  async getPatient(pid: string): Promise<PatientResponse> {
    return this.request<PatientResponse>(`/patients/${pid}`);
  }

  async updatePatient(
    pid: string,
    updateData: {
      symptoms?: SymptomAssessment;
      clinical_history?: ClinicalHistory;
      lab_data?: LabResults[];
    }
  ): Promise<PatientResponse> {
    return this.request<PatientResponse>(`/patients/${pid}`, {
      method: 'PUT',
      body: JSON.stringify(updateData),
    });
  }

  async lockPatientRecord(pid: string): Promise<{ message: string }> {
    return this.request<{ message: string }>(`/patients/${pid}/lock`, {
      method: 'POST',
    });
  }

  async searchPatients(filters?: {
    clinician_only?: boolean;
    date_from?: string;
    date_to?: string;
    min_completeness?: number;
  }): Promise<PatientResponse[]> {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, value.toString());
        }
      });
    }

    const query = params.toString();
    return this.request<PatientResponse[]>(`/patients/${query ? `?${query}` : ''}`);
  }

  // ML Predictions
  async getMLPrediction(features: PatientFeatures): Promise<MLPrediction> {
    return this.request<MLPrediction>('/ml/predict', {
      method: 'POST',
      body: JSON.stringify(features),
    });
  }

  async getModelInfo(): Promise<ModelInfo> {
    return this.request<ModelInfo>('/ml/models/info');
  }

  async retrainModels(): Promise<{
    status: string;
    performance: Record<string, number>;
    model_version: string;
    training_samples: number;
  }> {
    return this.request<{
      status: string;
      performance: Record<string, number>;
      model_version: string;
      training_samples: number;
    }>('/ml/models/retrain', {
      method: 'POST',
    });
  }

  // Data Export
  async exportTrainingData(format: 'csv' | 'json' = 'csv'): Promise<{
    status: string;
    message: string;
    filename: string;
    estimated_time_seconds: number;
  }> {
    return this.request<{
      status: string;
      message: string;
      filename: string;
      estimated_time_seconds: number;
    }>(`/exports/training-data?format=${format}`, {
      method: 'POST',
    });
  }

  async getExportStatus(filename: string): Promise<ExportStatus> {
    return this.request<ExportStatus>(`/exports/status/${filename}`);
  }
}

export const apiService = new ApiService();
```

### frontend/src/services/auth.ts
```typescript
import { apiService } from './api';
import { AuthResponse, User } from '../types/api';

class AuthService {
  private tokenKey = 'access_token';
  private userKey = 'current_user';

  async login(username: string, password: string): Promise<boolean> {
    try {
      const response = await apiService.login(username, password);
      localStorage.setItem(this.tokenKey, response.access_token);
      
      // Get user info
      const user = await apiService.getCurrentUser();
      localStorage.setItem(this.userKey, JSON.stringify(user));
      
      return true;
    } catch (error) {
      console.error('Login failed:', error);
      return false;
    }
  }

  logout(): void {
    localStorage.removeItem(this.tokenKey);
    localStorage.removeItem(this.userKey);
  }

  getToken(): string | null {
    return localStorage.getItem(this.tokenKey);
  }

  getCurrentUser(): User | null {
    const userJson = localStorage.getItem(this.userKey);
    return userJson ? JSON.parse(userJson) : null;
  }

  isAuthenticated(): boolean {
    return !!this.getToken();
  }

  hasRole(role: string): boolean {
    const user = this.getCurrentUser();
    return user ? user.roles.includes(role) : false;
  }
}

export const authService = new AuthService();
```

### frontend/src/hooks/useAuth.ts
```typescript
import { useState, useEffect } from 'react';
import { authService } from '../services/auth';
import { User } from '../types/api';

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
      setLoading(true);
      
      if (authService.isAuthenticated()) {
        const currentUser = authService.getCurrentUser();
        setUser(currentUser);
      }
      
      setLoading(false);
    };

    checkAuth();
  }, []);

  const login = async (username: string, password: string) => {
    const success = await authService.login(username, password);
    if (success) {
      setUser(authService.getCurrentUser());
    }
    return success;
  };

  const logout = () => {
    authService.logout();
    setUser(null);
  };

  return {
    user,
    loading,
    login,
    logout,
    isAuthenticated: !!user,
    hasRole: (role: string) => authService.hasRole(role),
  };
};
```

### frontend/src/hooks/usePatientData.ts
```typescript
import { useState, useEffect } from 'react';
import { apiService } from '../services/api';
import { PatientResponse } from '../types/api';

export const usePatientData = (pid?: string) => {
  const [patient, setPatient] = useState<PatientResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (pid) {
      fetchPatient(pid);
    }
  }, [pid]);

  const fetchPatient = async (patientId: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const data = await apiService.getPatient(patientId);
      setPatient(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch patient');
    } finally {
      setLoading(false);
    }
  };

  const updatePatient = async (updateData: any) => {
    if (!pid) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const updatedPatient = await apiService.updatePatient(pid, updateData);
      setPatient(updatedPatient);
      return updatedPatient;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update patient');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const lockPatient = async () => {
    if (!pid) return;
    
    setLoading(true);
    setError(null);
    
    try {
      await apiService.lockPatientRecord(pid);
      // Refresh patient data
      await fetchPatient(pid);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to lock patient');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    patient,
    loading,
    error,
    updatePatient,
    lockPatient,
    refetch: () => pid && fetchPatient(pid),
  };
};
```

### frontend/src/hooks/useMLPredictions.ts
```typescript
import { useState, useEffect } from 'react';
import { apiService } from '../services/api';
import { MLPrediction, ModelInfo, PatientFeatures } from '../types/ml';

export const useMLPredictions = (features?: PatientFeatures, autoRefresh = false) => {
  const [prediction, setPrediction] = useState<MLPrediction | null>(null);
  const [modelInfo, setModelInfo] = useState<ModelInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchModelInfo();
  }, []);

  useEffect(() => {
    if (features && autoRefresh) {
      fetchPrediction(features);
    }
  }, [features, autoRefresh]);

  const fetchModelInfo = async () => {
    try {
      const info = await apiService.getModelInfo();
      setModelInfo(info);
    } catch (err) {
      console.error('Failed to fetch model info:', err);
    }
  };

  const fetchPrediction = async (patientFeatures: PatientFeatures) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await apiService.getMLPrediction(patientFeatures);
      setPrediction(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to get prediction');
    } finally {
      setLoading(false);
    }
  };

  const retrainModels = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await apiService.retrainModels();
      // Refresh model info
      await fetchModelInfo();
      return result;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to retrain models');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    prediction,
    modelInfo,
    loading,
    error,
    fetchPrediction,
    retrainModels,
  };
};
```

### frontend/src/components/AssessmentWizard.tsx
```typescript
import React, { useState } from 'react';
import { PatientDemographics, SymptomAssessment, ClinicalHistory, LabResults } from '../types/clinical';
import { MLPrediction } from '../types/ml';
import { useMLPredictions } from '../hooks/useMLPredictions';
import { apiService } from '../services/api';

interface AssessmentWizardProps {
  onComplete?: (patientData: any) => void;
  initialPid?: string;
}

export const AssessmentWizard: React.FC<AssessmentWizardProps> = ({ onComplete, initialPid }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [patientData, setPatientData] = useState<any>({});
  const [mlPrediction, setMLPrediction] = useState<MLPrediction | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Real-time ML predictions as user fills forms
  const { prediction, loading: predictionLoading, fetchPrediction } = useMLPredictions(
    patientData, 
    true
  );

  const steps = [
    'Demographics',
    'Symptoms',
    'Clinical History',
    'Lab Data',
    'Review & Submit'
  ];

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleDataChange = (stepData: any) => {
    const updatedData = { ...patientData, ...stepData };
    setPatientData(updatedData);
    
    // Update ML prediction when data changes
    if (currentStep > 0) {
      fetchPrediction(updatedData);
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    try {
      // Create or update patient
      if (initialPid) {
        await apiService.updatePatient(initialPid, patientData);
      } else {
        await apiService.createPatient({
          pid: patientData.demographics.pid,
          demographics: patientData.demographics,
          clinician_id: 'current_user', // This would come from auth context
        });
        
        // Then add the rest of the data
        await apiService.updatePatient(patientData.demographics.pid, {
          symptoms: patientData.symptoms,
          clinical_history: patientData.clinicalHistory,
          lab_data: patientData.labs,
        });
      }
      
      if (onComplete) {
        onComplete(patientData);
      }
    } catch (error) {
      console.error('Failed to submit assessment:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return <DemographicsForm data={patientData.demographics} onChange={handleDataChange} />;
      case 1:
        return <SymptomAssessmentForm data={patientData.symptoms} onChange={handleDataChange} />;
      case 2:
        return <ClinicalHistoryForm data={patientData.clinicalHistory} onChange={handleDataChange} />;
      case 3:
        return <LabDataForm data={patientData.labs} onChange={handleDataChange} />;
      case 4:
        return <ReviewStep data={patientData} prediction={prediction} />;
      default:
        return null;
    }
  };

  return (
    <div className="assessment-wizard max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-800 mb-2">Patient Assessment</h1>
        <div className="flex items-center justify-between mb-4">
          {steps.map((step, index) => (
            <div key={index} className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                index <= currentStep ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'
              }`}>
                {index + 1}
              </div>
              {index < steps.length - 1 && (
                <div className={`w-16 h-1 ${index < currentStep ? 'bg-blue-600' : 'bg-gray-200'}`}></div>
              )}
            </div>
          ))}
        </div>
        <div className="text-center text-sm text-gray-600">
          Step {currentStep + 1} of {steps.length}: {steps[currentStep]}
        </div>
      </div>

      <div className="mb-8">
        {renderStep()}
      </div>

      {/* Real-time ML insights panel */}
      {prediction && (
        <div className="mb-8">
          <MLInsightsPanel prediction={prediction} loading={predictionLoading} />
        </div>
      )}

      <div className="flex justify-between">
        <button
          onClick={handlePrevious}
          disabled={currentStep === 0}
          className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 disabled:opacity-50"
        >
          Previous
        </button>
        
        {currentStep < steps.length - 1 ? (
          <button
            onClick={handleNext}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Next
          </button>
        ) : (
          <button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
          >
            {isSubmitting ? 'Submitting...' : 'Complete Assessment'}
          </button>
        )}
      </div>
    </div>
  );
};

// Placeholder components for each step
const DemographicsForm: React.FC<{ data: any; onChange: (data: any) => void }> = ({ data, onChange }) => {
  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">Patient Demographics</h2>
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Patient ID</label>
          <input
            type="text"
            value={data?.pid || ''}
            onChange={(e) => onChange({ demographics: { ...data, pid: e.target.value } })}
            className="w-full p-2 border border-gray-300 rounded"
          />
        </div>
        {/* More form fields would go here */}
      </div>
    </div>
  );
};

const SymptomAssessmentForm: React.FC<{ data: any; onChange: (data: any) => void }> = ({ data, onChange }) => {
  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">Symptom Assessment</h2>
      <p>Symptom assessment form would go here</p>
    </div>
  );
};

const ClinicalHistoryForm: React.FC<{ data: any; onChange: (data: any) => void }> = ({ data, onChange }) => {
  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">Clinical History</h2>
      <p>Clinical history form would go here</p>
    </div>
  );
};

const LabDataForm: React.FC<{ data: any; onChange: (data: any) => void }> = ({ data, onChange }) => {
  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">Laboratory Data</h2>
      <p>Lab data form would go here</p>
    </div>
  );
};

const ReviewStep: React.FC<{ data: any; prediction: MLPrediction | null }> = ({ data, prediction }) => {
  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">Review & Submit</h2>
      <p>Review step would go here</p>
    </div>
  );
};
```

### frontend/src/components/MLInsightsPanel.tsx
```typescript
import React from 'react';
import { MLPrediction } from '../types/ml';

interface MLInsightsPanelProps {
  prediction: MLPrediction;
  loading?: boolean;
}

export const MLInsightsPanel: React.FC<MLInsightsPanelProps> = ({ prediction, loading = false }) => {
  if (loading) {
    return (
      <div className="ml-insights bg-blue-50 p-4 rounded-lg border border-blue-200">
        <div className="flex items-center">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
          <span className="text-blue-800">Analyzing data for clinical insights...</span>
        </div>
      </div>
    );
  }

  if (!prediction) {
    return null;
  }

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'High': return 'text-red-600';
      case 'Medium': return 'text-yellow-600';
      case 'Low': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  const getSeverityColor = (score: number) => {
    if (score >= 7) return 'bg-red-600';
    if (score >= 4) return 'bg-yellow-600';
    return 'bg-green-600';
  };

  return (
    <div className="ml-insights bg-blue-50 p-4 rounded-lg border border-blue-200">
      <h3 className="font-bold text-blue-900 mb-3 flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clipRule="evenodd" />
        </svg>
        AI Clinical Insights
      </h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div className="bg-white p-3 rounded-lg shadow-sm">
          <span className="text-sm text-gray-600">Suggested Diagnosis:</span>
          <p className="font-semibold text-lg">{prediction.likely_diagnosis}</p>
          <div className="mt-1">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full"
                style={{ width: `${prediction.confidence_scores.diagnosis * 100}%` }}
              />
            </div>
            <span className="text-xs text-gray-500">
              Confidence: {(prediction.confidence_scores.diagnosis * 100).toFixed(1)}%
            </span>
          </div>
        </div>
        
        <div className="bg-white p-3 rounded-lg shadow-sm">
          <span className="text-sm text-gray-600">Risk Level:</span>
          <p className={`font-semibold text-lg ${getRiskColor(prediction.risk_level)}`}>
            {prediction.risk_level}
          </p>
          <div className="mt-1">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${getRiskColor(prediction.risk_level).replace('text', 'bg')}`}
                style={{ width: `${prediction.confidence_scores.risk * 100}%` }}
              />
            </div>
            <span className="text-xs text-gray-500">
              Confidence: {(prediction.confidence_scores.risk * 100).toFixed(1)}%
            </span>
          </div>
        </div>
      </div>
      
      <div className="bg-white p-3 rounded-lg shadow-sm mb-4">
        <div className="text-sm text-gray-600 mb-1">Severity Score</div>
        <div className="flex items-center">
          <div className="w-full bg-gray-200 rounded-full h-3 mr-3">
            <div 
              className={`h-3 rounded-full ${getSeverityColor(prediction.severity_score)}`}
              style={{ width: `${(prediction.severity_score / 10) * 100}%` }}
            />
          </div>
          <span className="text-sm font-medium">
            {prediction.severity_score.toFixed(1)}/10
          </span>
        </div>
      </div>
      
      {prediction.influential_features && prediction.influential_features.length > 0 && (
        <div className="bg-white p-3 rounded-lg shadow-sm">
          <h4 className="font-medium text-gray-800 mb-2">Most Influential Factors</h4>
          <div className="space-y-2">
            {prediction.influential_features.slice(0, 3).map((feature, index) => (
              <div key={index} className="flex justify-between text-sm">
                <span className="text-gray-700">{feature.feature.replace(/_/g, ' ')}</span>
                <span className="font-medium">
                  {feature.value.toFixed(2)} 
                  <span className="text-gray-500 ml-1">
                    ({(feature.importance * 100).toFixed(1)}%)
                  </span>
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
      
      <div className="mt-3 text-xs text-gray-500 text-right">
        Model version: {prediction.model_version}
      </div>
    </div>
  );
};
```

### frontend/src/components/PatientSearch.tsx
```typescript
import React, { useState, useEffect } from 'react';
import { PatientResponse } from '../types/api';
import { apiService } from '../services/api';

interface PatientSearchProps {
  onPatientSelect?: (patient: PatientResponse) => void;
}

export const PatientSearch: React.FC<PatientSearchProps> = ({ onPatientSelect }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [patients, setPatients] = useState<PatientResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);

  useEffect(() => {
    if (searchTerm.length > 2) {
      searchPatients();
    } else {
      setPatients([]);
      setShowResults(false);
    }
  }, [searchTerm]);

  const searchPatients = async () => {
    setLoading(true);
    
    try {
      const results = await apiService.searchPatients({
        min_completeness: 0.5,
      });
      
      // Filter by search term (this would ideally be done on the server)
      const filtered = results.filter(patient => 
        patient.pid.toLowerCase().includes(searchTerm.toLowerCase())
      );
      
      setPatients(filtered);
      setShowResults(true);
    } catch (error) {
      console.error('Failed to search patients:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePatientClick = (patient: PatientResponse) => {
    onPatientSelect?.(patient);
    setShowResults(false);
    setSearchTerm('');
  };

  return (
    <div className="relative">
      <div className="relative">
        <input
          type="text"
          placeholder="Search by Patient ID..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full p-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 absolute left-3 top-2.5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
        </svg>
        {loading && (
          <div className="absolute right-3 top-2.5">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
          </div>
        )}
      </div>
      
      {showResults && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto">
          {patients.length === 0 ? (
            <div className="p-3 text-gray-500 text-center">
              {searchTerm.length > 2 ? 'No patients found' : 'Type at least 3 characters to search'}
            </div>
          ) : (
            patients.map((patient) => (
              <div
                key={patient.id}
                className="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                onClick={() => handlePatientClick(patient)}
              >
                <div className="font-medium">{patient.pid}</div>
                <div className="text-sm text-gray-600 flex justify-between">
                  <span>{patient.demographics?.age_group} {patient.demographics?.gender}</span>
                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                    {(patient.completeness_score * 100).toFixed(0)}% complete
                  </span>
                </div>
              </div>
            ))
          )}
        </div>
      )}
    </div>
  );
};
```

### frontend/src/components/DataExport.tsx
```typescript
import React, { useState } from 'react';
import { useMLPredictions } from '../hooks/useMLPredictions';
import { apiService } from '../services/api';
import { ExportStatus } from '../types/api';

export const DataExport: React.FC = () => {
  const [exportFormat, setExportFormat] = useState<'csv' | 'json'>('csv');
  const [exportStatus, setExportStatus] = useState<ExportStatus | null>(null);
  const [isExporting, setIsExporting] = useState(false);
  const [statusCheckInterval, setStatusCheckInterval] = useState<NodeJS.Timeout | null>(null);
  
  const { modelInfo } = useMLPredictions();

  const handleExport = async () => {
    setIsExporting(true);
    
    try {
      const response = await apiService.exportTrainingData(exportFormat);
      setExportStatus({
        status: 'processing',
        filename: response.filename,
        message: response.message,
      });
      
      // Start checking status
      const interval = setInterval(async () => {
        const status = await apiService.getExportStatus(response.filename);
        setExportStatus(status);
        
        if (status.status === 'completed') {
          clearInterval(interval);
          setStatusCheckInterval(null);
        }
      }, 2000);
      
      setStatusCheckInterval(interval);
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const downloadFile = () => {
    if (exportStatus?.status === 'completed' && exportStatus.filename) {
      const link = document.createElement('a');
      link.href = `/api/v1/exports/download/${exportStatus.filename}`;
      link.download = exportStatus.filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  React.useEffect(() => {
    // Clean up interval on unmount
    return () => {
      if (statusCheckInterval) {
        clearInterval(statusCheckInterval);
      }
    };
  }, [statusCheckInterval]);

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-semibold mb-4">Data Export</h2>
      
      <div className="mb-6">
        <h3 className="font-medium text-gray-700 mb-2">Export Format</h3>
        <div className="flex space-x-4">
          <label className="flex items-center">
            <input
              type="radio"
              name="format"
              value="csv"
              checked={exportFormat === 'csv'}
              onChange={() => setExportFormat('csv')}
              className="mr-2"
            />
            CSV (Spreadsheet)
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="format"
              value="json"
              checked={exportFormat === 'json'}
              onChange={() => setExportFormat('json')}
              className="mr-2"
            />
            JSON (Raw Data)
          </label>
        </div>
      </div>
      
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-medium text-gray-700 mb-2">Dataset Summary</h3>
        {modelInfo?.training_metadata ? (
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Training Samples:</span>
              <span className="ml-2 font-medium">{modelInfo.training_metadata.training_samples}</span>
            </div>
            <div>
              <span className="text-gray-600">Features:</span>
              <span className="ml-2 font-medium">{modelInfo.training_metadata.feature_count}</span>
            </div>
            <div>
              <span className="text-gray-600">Diagnosis Classes:</span>
              <span className="ml-2 font-medium">{modelInfo.training_metadata.diagnosis_classes.length}</span>
            </div>
            <div>
              <span className="text-gray-600">Last Trained:</span>
              <span className="ml-2 font-medium">
                {new Date(modelInfo.training_metadata.training_date).toLocaleDateString()}
              </span>
            </div>
          </div>
        ) : (
          <p className="text-gray-500">No model information available</p>
        )}
      </div>
      
      <div className="flex items-center space-x-4">
        <button
          onClick={handleExport}
          disabled={isExporting || exportStatus?.status === 'processing'}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 flex items-center"
        >
          {isExporting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Preparing Export...
            </>
          ) : (
            'Export Data'
          )}
        </button>
        
        {exportStatus?.status === 'completed' && (
          <button
            onClick={downloadFile}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 flex items-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
            Download
          </button>
        )}
      </div>
      
      {exportStatus && (
        <div className={`mt-4 p-3 rounded-lg ${
          exportStatus.status === 'processing' 
            ? 'bg-blue-50 text-blue-800' 
            : 'bg-green-50 text-green-800'
        }`}>
          <div className="font-medium">
            {exportStatus.status === 'processing' ? 'Export in Progress' : 'Export Complete'}
          </div>
          <div className="text-sm mt-1">
            {exportStatus.status === 'processing' ? (
              <>{exportStatus.message}</>
            ) : (
              <>File: {exportStatus.filename} ({(exportStatus.size_bytes! / 1024).toFixed(2)} KB)</>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
```

### frontend/src/App.tsx
```typescript
import React, { useState } from 'react';
import { useAuth } from './hooks/useAuth';
import { AssessmentWizard } from './components/AssessmentWizard';
import { PatientSearch } from './components/PatientSearch';
import { DataExport } from './components/DataExport';
import { PatientResponse } from './types/api';

const LoginForm: React.FC<{ onLogin: (username: string, password: string) => Promise<boolean> }> = ({ onLogin }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    
    try {
      const success = await onLogin(username, password);
      if (!success) {
        setError('Invalid username or password');
      }
    } catch (err) {
      setError('Login failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8 p-8 bg-white rounded-lg shadow-md">
        <div>
          <h1 className="text-2xl font-bold text-center text-gray-900">Psychiatric ML System</h1>
          <p className="mt-2 text-center text-gray-600">Sign in to your account</p>
        </div>
        
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          {error && (
            <div className="bg-red-50 text-red-700 p-3 rounded-md">
              {error}
            </div>
          )}
          
          <div>
            <label htmlFor="username" className="block text-sm font-medium text-gray-700">
              Username
            </label>
            <input
              id="username"
              name="username"
              type="text"
              required
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700">
              Password
            </label>
            <input
              id="password"
              name="password"
              type="password"
              required
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <div>
            <button
              type="submit"
              disabled={loading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {loading ? 'Signing in...' : 'Sign in'}
            </button>
          </div>
          
          <div className="text-center text-sm text-gray-600">
            Demo credentials: username: <span className="font-mono">demo</span>, password: <span className="font-mono">password</span>
          </div>
        </form>
      </div>
    </div>
  );
};

const Dashboard: React.FC<{ 
  user: any; 
  onLogout: () => void;
  onPatientSelect?: (patient: PatientResponse) => void;
}> = ({ user, onLogout, onPatientSelect }) => {
  const [activeTab, setActiveTab] = useState<'assessment' | 'export'>('assessment');
  const [selectedPatient, setSelectedPatient] = useState<PatientResponse | null>(null);

  const handlePatientSelect = (patient: PatientResponse) => {
    setSelectedPatient(patient);
    onPatientSelect?.(patient);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8 flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Psychiatric ML Data Collection</h1>
            <p className="text-sm text-gray-600">Welcome, {user.username}</p>
          </div>
          <button
            onClick={onLogout}
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 flex items-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clipRule="evenodd" />
            </svg>
            Logout
          </button>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('assessment')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'assessment'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Patient Assessment
              </button>
              {user.roles.includes('admin') || user.roles.includes('researcher') ? (
                <button
                  onClick={() => setActiveTab('export')}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'export'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Data Export
                </button>
              ) : null}
            </nav>
          </div>
        </div>

        {activeTab === 'assessment' && (
          <div>
            <div className="mb-6">
              <PatientSearch onPatientSelect={handlePatientSelect} />
            </div>
            
            <AssessmentWizard 
              initialPid={selectedPatient?.pid}
              onComplete={() => {
                setSelectedPatient(null);
              }}
            />
          </div>
        )}

        {activeTab === 'export' && <DataExport />}
      </main>
    </div>
  );
};

const App: React.FC = () => {
  const { user, login, logout, isAuthenticated, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <>
      {isAuthenticated && user ? (
        <Dashboard user={user} onLogout={logout} />
      ) : (
        <LoginForm onLogin={login} />
      )}
    </>
  );
};

export default App;
```

### frontend/src/index.tsx
```typescript
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import './index.css';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
```

### frontend/src/index.css
```css
@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Custom styles for form elements */
input[type="text"], 
input[type="password"], 
input[type="number"], 
select, 
textarea {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
}

label {
  @apply block text-sm font-medium text-gray-700;
}

button {
  @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2;
}

/* Loading spinner */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
```

### frontend/tailwind.config.js
```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'blue': {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
      },
    },
  },
  plugins: [],
}
```

### frontend/package.json
```json
{
  "name": "psychiatric-ml-frontend",
  "version": "0.1.0",
  "private": true,
  "dependencies": {
    "@types/node": "^16.18.0",
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-scripts": "5.0.1",
    "typescript": "^4.9.0",
    "web-vitals": "^2.1.4"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject"
  },
  "eslintConfig": {
    "extends": [
      "react-app",
      "react-app/jest"
    ]
  },
  "browserslist": {
    "production": [
      ">0.2%",
      "not dead",
      "not op_mini all"
    ],
    "development": [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  },
  "devDependencies": {
    "tailwindcss": "^3.3.0",
    "autoprefixer": "^10.4.14",
    "postcss": "^8.4.24"
  },
  "proxy": "http://localhost:8000"
}
```

### frontend/public/index.html
```html
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Psychiatric ML Data Collection System" />
    <title>Psychiatric ML System</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>
```
 **Frontend Components**:
   - React/TypeScript application with Tailwind CSS styling
   - Authentication system with login/logout functionality
   - Patient assessment wizard with real-time ML insights
   - Patient search functionality
   - Data export capabilities
   - Custom hooks for state management