// Core data types for the psychiatric ML system

export interface User {
  username: string;
  user_id: string;
  full_name: string;
  roles: string[];
}

export interface AuthToken {
  access_token: string;
  token_type: string;
  expires_in: number;
  user_info: User;
}

export interface PatientDemographics {
  pid: string;
  age_group: "18-25" | "26-35" | "36-45" | "46-55" | "56-65" | "65+";
  gender: "Male" | "Female" | "Non-binary" | "Other" | "Undisclosed";
  education: "Primary" | "Secondary" | "Bachelor" | "Graduate" | "Professional";
  occupation: "Healthcare" | "Education" | "Technology" | "Service" | "Manual" | "Student" | "Unemployed" | "Retired" | "Other";
  marital_status: "Single" | "Married" | "Divorced" | "Widowed" | "Separated";
  living_situation: "Alone" | "Family" | "Shared" | "Assisted" | "Institutional";
  referral_source: "Self" | "GP" | "Specialist" | "Emergency" | "Court" | "Other";
  insurance_type: "Private" | "Public" | "Uninsured" | "Unknown";
  created_at?: string;
  clinician_id: string;
  data_quality_score?: number;
}

export interface SymptomItem {
  present: boolean;
  severity?: "Mild" | "Moderate" | "Severe" | null;
  duration_weeks?: number | null;
  functional_impact?: "None" | "Mild" | "Moderate" | "Severe" | null;
}

export interface SymptomAssessment {
  pid: string;
  assessment_date?: string;
  mood_symptoms: Record<string, SymptomItem>;
  psychotic_symptoms: Record<string, SymptomItem>;
  anxiety_symptoms: Record<string, SymptomItem>;
  cognitive_symptoms: Record<string, SymptomItem>;
  behavioral_symptoms: Record<string, SymptomItem>;
  functioning_level: number; // 1-100 GAF-style score
  symptom_severity_index: number; // 0-10 scale
}

export interface SubstanceUseItem {
  current_use: boolean;
  frequency?: "Daily" | "Weekly" | "Monthly" | "Occasional" | null;
  severity?: "Mild" | "Moderate" | "Severe" | null;
  years_of_use?: number | null;
}

export interface ClinicalHistory {
  pid: string;
  previous_diagnoses: string[]; // ICD-11 codes
  hospitalization_count: number;
  suicide_attempts: number;
  medication_trials: Record<string, string>; // drug_class -> response
  therapy_history: string[]; // CBT, DBT, etc.
  family_psychiatric_history: boolean;
  family_conditions: string[];
  substance_use: Record<string, SubstanceUseItem>;
  medical_conditions: string[];
  current_medications: string[];
  trauma_history: boolean;
  social_support_level: "High" | "Moderate" | "Low" | "Minimal";
  housing_stability: "Stable" | "Temporary" | "Unstable" | "Homeless";
}

export interface LabResults {
  pid: string;
  collection_date: string;
  lab_type: "Basic_Metabolic" | "CBC" | "Liver_Function" | "Thyroid" | "Toxicology" | "Inflammatory_Markers";
  raw_values: Record<string, number | null>;
  reference_ranges: Record<string, [number, number]>;
  normalized_values?: Record<string, number | null>;
  abnormal_flags?: Record<string, number>;
  clinical_significance?: Record<string, boolean>;
}

export interface Patient {
  pid: string;
  demographics: PatientDemographics;
  symptoms: SymptomAssessment;
  clinical_history: ClinicalHistory;
  created_at: string;
  updated_at: string;
  clinician_id: string;
  completeness_score: number;
}

export interface PatientSummary {
  pid: string;
  created_at: string;
  updated_at: string;
  completeness_score: number;
  is_locked: boolean;
}

export interface MLPrediction {
  diagnosis?: {
    predicted_class: string;
    confidence: number;
    probability_distribution: Record<string, number>;
  };
  severity?: {
    predicted_score: number;
    severity_level: string;
  };
  risk?: {
    predicted_class: string;
    confidence: number;
    probability_distribution: Record<string, number>;
  };
  metadata: {
    prediction_timestamp: string;
    model_version: string;
    feature_count: number;
    data_completeness: number;
  };
}

export interface MLModelStatus {
  is_trained: boolean;
  model_performance: Record<string, number>;
  training_metadata: {
    training_date?: string;
    n_samples?: number;
    n_features?: number;
    feature_names?: string[];
  };
  feature_count: number;
}

export interface ExportRequest {
  format: "csv" | "json" | "parquet";
  include_demographics: boolean;
  include_symptoms: boolean;
  include_history: boolean;
  include_predictions: boolean;
  date_from?: string;
  date_to?: string;
  anonymize: boolean;
}

export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  error?: string;
  status: number;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  per_page: number;
  total_pages: number;
}

// Form validation types
export interface ValidationError {
  field: string;
  message: string;
}

export interface FormState<T> {
  data: T;
  errors: ValidationError[];
  isSubmitting: boolean;
  isDirty: boolean;
}

// UI state types
export interface LoadingState {
  isLoading: boolean;
  message?: string;
}

export interface AlertState {
  type: "success" | "warning" | "danger" | "info";
  message: string;
  visible: boolean;
}

// Navigation types
export interface NavItem {
  name: string;
  href: string;
  icon?: React.ComponentType<any>;
  current?: boolean;
  children?: NavItem[];
}

// Chart data types for analytics
export interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
}

export interface TimeSeriesDataPoint {
  date: string;
  value: number;
  category?: string;
}

// Symptom domain definitions
export const SYMPTOM_DOMAINS = {
  mood_symptoms: [
    'depressed_mood',
    'anhedonia',
    'fatigue',
    'worthlessness',
    'concentration_difficulty',
    'sleep_disturbance',
    'appetite_change',
    'psychomotor_changes',
    'suicidal_ideation'
  ],
  psychotic_symptoms: [
    'auditory_hallucinations',
    'visual_hallucinations',
    'delusions_persecution',
    'delusions_grandiose',
    'thought_disorder',
    'disorganized_behavior',
    'negative_symptoms'
  ],
  anxiety_symptoms: [
    'excessive_worry',
    'restlessness',
    'fatigue',
    'concentration_difficulty',
    'irritability',
    'muscle_tension',
    'sleep_disturbance',
    'panic_attacks'
  ],
  cognitive_symptoms: [
    'memory_impairment',
    'attention_deficit',
    'executive_dysfunction',
    'processing_speed_reduction',
    'language_difficulties'
  ],
  behavioral_symptoms: [
    'aggression',
    'self_harm',
    'substance_abuse',
    'social_withdrawal',
    'compulsive_behaviors',
    'risk_taking'
  ]
} as const;

export type SymptomDomain = keyof typeof SYMPTOM_DOMAINS;
export type SymptomName = typeof SYMPTOM_DOMAINS[SymptomDomain][number];
