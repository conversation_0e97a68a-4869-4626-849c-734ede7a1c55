from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from jose import <PERSON><PERSON><PERSON><PERSON><PERSON>, jwt
from passlib.context import Crypt<PERSON>ontext
from fastapi import HTT<PERSON>Exception, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from config import settings
from utils.logging import logger

# Password hashing
pwd_context = CryptContext(schemes=["argon2"], deprecated="auto")

# JWT token security
security = HTTPBearer()

class AuthManager:
    def __init__(self):
        self.secret_key = settings.secret_key
        self.algorithm = settings.jwt_algorithm
        self.access_token_expire_minutes = settings.jwt_expire_minutes
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash"""
        return pwd_context.verify(plain_password, hashed_password)
    
    def get_password_hash(self, password: str) -> str:
        """Hash a password"""
        return pwd_context.hash(password)
    
    def create_access_token(self, data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        """Create a JWT access token"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify and decode a JWT token"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except JWTError as e:
            logger.warning(f"Token verification failed: {e}", extra={"security": True})
            return None

# Global auth manager instance
auth_manager = AuthManager()

# Demo users for development (in production, this would come from a database)
DEMO_USERS = {
    "demo": {
        "username": "demo",
        "user_id": "demo_user",
        "hashed_password": auth_manager.get_password_hash("password"),
        "roles": ["clinician"],
        "full_name": "Demo Clinician"
    },
    "admin": {
        "username": "admin",
        "user_id": "admin_user",
        "hashed_password": auth_manager.get_password_hash("admin123"),
        "roles": ["admin", "clinician"],
        "full_name": "System Administrator"
    },
    "researcher": {
        "username": "researcher",
        "user_id": "researcher_user",
        "hashed_password": auth_manager.get_password_hash("research123"),
        "roles": ["researcher", "clinician"],
        "full_name": "Research Clinician"
    }
}

def authenticate_user(username: str, password: str) -> Optional[Dict[str, Any]]:
    """Authenticate a user with username and password"""
    user = DEMO_USERS.get(username)
    if not user:
        return None
    
    if not auth_manager.verify_password(password, user["hashed_password"]):
        return None
    
    return user

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """Get the current authenticated user from JWT token"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = auth_manager.verify_token(credentials.credentials)
        if payload is None:
            raise credentials_exception
        
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        
        user = DEMO_USERS.get(username)
        if user is None:
            raise credentials_exception
        
        return user
    
    except JWTError:
        raise credentials_exception

async def get_current_active_user(current_user: Dict[str, Any] = Depends(get_current_user)) -> Dict[str, Any]:
    """Get the current active user (additional checks can be added here)"""
    return current_user
