"""
Dependency injection functions for FastAPI application
"""
from database.connection import SurrealDBManager
from security.pid_manager import SecurePIDHandler
from ml.pipeline import PsychiatricMLPipeline

# Global instances (will be set by main.py)
db_manager = None
ml_pipeline = None

async def get_db():
    """Dependency to get database manager"""
    return db_manager

async def get_ml_pipeline():
    """Dependency to get ML pipeline"""
    return ml_pipeline

def get_pid_handler():
    """Dependency to get PID handler"""
    return SecurePIDHandler()

def set_global_instances(db_mgr, ml_pipe):
    """Set global instances from main.py"""
    global db_manager, ml_pipeline
    db_manager = db_mgr
    ml_pipeline = ml_pipe
