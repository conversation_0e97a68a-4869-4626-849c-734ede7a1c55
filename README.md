# Psychiatric ML Data Collection System

A high-performance, privacy-first psychiatric data collection application with dual purposes:
- **Training Phase**: Collect structured clinical data for ML model development
- **Inference Phase**: Use trained models for real-time clinical decision support

## Features

### Core Functionality
- **Privacy-First Design**: Irreversible PID pseudonymization with military-grade security
- **Structured Data Collection**: DSM-5-TR aligned symptom assessments
- **ML Pipeline**: Real-time predictions for diagnosis, severity, and risk stratification
- **Multi-Format Export**: CSV, JSON, and Parquet export capabilities
- **Role-Based Access**: Clinician, researcher, and admin roles

### Technical Stack
- **Backend**: FastAPI with SurrealDB
- **Frontend**: React with TypeScript and TailwindCSS
- **ML**: scikit-learn with comprehensive feature engineering
- **Security**: JWT authentication, encrypted PIDs, audit logging

## Quick Start

### Prerequisites
- Python 3.8+
- Node.js 16+
- SurrealDB (or use in-memory mode for development)

### Backend Setup

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Set environment variables** (optional for development):
   ```bash
   export SECRET_KEY="your-secret-key"
   export CLINIC_SECRET="your-clinic-secret"
   ```

3. **Start the backend**:
   ```bash
   cd backend
   python main.py
   ```

   The API will be available at `http://localhost:8000`
   - API Documentation: `http://localhost:8000/docs`
   - Health Check: `http://localhost:8000/health`

### Frontend Setup

1. **Install dependencies**:
   ```bash
   cd frontend
   npm install
   ```

2. **Start the development server**:
   ```bash
   npm start
   ```

   The frontend will be available at `http://localhost:3000`

## Demo Accounts

The system comes with pre-configured demo accounts:

- **Admin**: `admin` / `admin123`
  - Full system access, user management, model training
- **Clinician**: `demo` / `password`
  - Patient data entry and basic analytics
- **Researcher**: `researcher` / `research123`
  - Data export, ML analytics, advanced features

## API Endpoints

### Authentication
- `POST /api/v1/auth/login` - User login
- `GET /api/v1/auth/me` - Get current user info
- `POST /api/v1/auth/logout` - User logout

### Patient Management
- `POST /api/v1/patients/create` - Create new patient
- `GET /api/v1/patients/{pid}` - Get patient details
- `PUT /api/v1/patients/{pid}/symptoms` - Update symptoms
- `PUT /api/v1/patients/{pid}/history` - Update clinical history
- `GET /api/v1/patients/search` - Search patients

### ML Predictions
- `POST /api/v1/ml/predict` - Generate predictions
- `POST /api/v1/ml/train` - Train models
- `GET /api/v1/ml/status` - Get model status
- `POST /api/v1/ml/feedback` - Submit prediction feedback

### Data Export
- `POST /api/v1/exports/request` - Request data export
- `GET /api/v1/exports/formats` - Get available formats

## Security Features

### PID Management
- **Format**: `PID_[16char_data][4char_checksum]`
- **Generation**: HMAC-SHA256 with clinic-specific keys
- **Validation**: CRC32 checksum verification
- **Search**: Separate search tokens for database queries

### Authentication
- **JWT Tokens**: Secure session management
- **Role-Based Access**: Granular permission system
- **Audit Logging**: Comprehensive security event tracking

### Data Protection
- **Encryption**: All PIDs are irreversibly encrypted
- **Validation**: Comprehensive input validation
- **Sanitization**: SQL injection and XSS protection

## ML Pipeline

### Feature Engineering
- **Demographics**: One-hot and ordinal encoding
- **Symptoms**: Severity scoring and domain analysis
- **Clinical History**: Risk factor quantification
- **Lab Results**: Normalized values and abnormality flags

### Models
- **Diagnosis Predictor**: Random Forest Classifier
- **Severity Predictor**: Random Forest Regressor
- **Risk Stratifier**: Logistic Regression

### Performance Monitoring
- Cross-validation metrics
- Feature importance analysis
- Model drift detection
- Prediction confidence scoring

## Development

### Project Structure
```
psychiatric_ml_system/
├── backend/
│   ├── main.py                 # FastAPI application
│   ├── config.py              # Configuration management
│   ├── database/              # SurrealDB models and connection
│   ├── ml/                    # ML pipeline and features
│   ├── api/                   # API endpoints
│   ├── security/              # Authentication and PID management
│   └── utils/                 # Utilities and validation
├── frontend/
│   ├── src/
│   │   ├── components/        # React components
│   │   ├── contexts/          # React contexts
│   │   ├── services/          # API services
│   │   └── types/             # TypeScript types
│   └── public/
├── models/                    # Trained ML models
├── data/                      # Database files and exports
└── requirements.txt
```

### Running Tests

Backend tests:
```bash
cd backend
pytest
```

Frontend tests:
```bash
cd frontend
npm test
```

### Building for Production

Backend:
```bash
# Set production environment variables
export ENVIRONMENT=production
export SECRET_KEY="your-production-secret"
export CLINIC_SECRET="your-production-clinic-secret"

# Run with production ASGI server
uvicorn main:app --host 0.0.0.0 --port 8000
```

Frontend:
```bash
cd frontend
npm run build
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `SECRET_KEY` | JWT signing key | Auto-generated in dev |
| `CLINIC_SECRET` | PID encryption key | Auto-generated in dev |
| `SURREALDB_URL` | Database connection URL | `memory://` |
| `LOG_LEVEL` | Logging level | `INFO` |
| `ENVIRONMENT` | Environment mode | `development` |

### Database Configuration

The system supports multiple SurrealDB configurations:
- **Memory**: `memory://` (development)
- **File**: `file://data/database.db`
- **Remote**: `ws://localhost:8000/rpc`

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check SurrealDB is running
   - Verify connection URL in config
   - Check network connectivity

2. **Authentication Errors**
   - Verify SECRET_KEY is set
   - Check token expiration
   - Validate user credentials

3. **ML Model Errors**
   - Ensure sufficient training data (>50 samples)
   - Check model files exist in `/models`
   - Verify feature compatibility

### Debug Mode

Enable debug mode for detailed error messages:
```bash
export DEBUG=true
```

### Logs

Application logs are stored in:
- `./logs/app.log` - General application logs
- `./logs/errors.log` - Error logs
- `./logs/security.log` - Security audit logs

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Check the documentation at `/docs`
- Review the API documentation at `http://localhost:8000/docs`
- Check the health endpoint at `http://localhost:8000/health`
