# Conda Environment Auto-Activation Setup Guide

## Issue Diagnosis

Based on the error you're experiencing, there's a conda installation corruption related to OpenSSL/cryptography libraries. This is preventing conda from working properly.

## Root Cause

The error `AttributeError: module 'lib' has no attribute 'X509_V_FLAG_NOTIFY_POLICY'` indicates a conflict between different versions of OpenSSL libraries, commonly caused by:

1. Multiple Python installations
2. Conflicting cryptography packages
3. Corrupted conda installation
4. Windows system library conflicts

## Solution Steps

### Step 1: Fix Conda Installation

**Option A: Repair Conda (Recommended)**
```bash
# 1. Open Command Prompt as Administrator
# 2. Navigate to your project directory
cd "C:\Users\<USER>\projects\fast api sureal"

# 3. Try to repair conda
conda update conda
conda update --all

# 4. If that fails, reinstall cryptography
conda install --force-reinstall cryptography
conda install --force-reinstall pyopenssl
```

**Option B: Clean Reinstall (If Option A fails)**
```bash
# 1. Uninstall current conda
# Go to Control Panel > Programs > Uninstall Anaconda/Miniconda

# 2. Download fresh Miniconda from: https://docs.conda.io/en/latest/miniconda.html
# 3. Install with default settings
# 4. Restart your computer
```

### Step 2: Create and Configure Environment

Once conda is working:

```bash
# 1. Navigate to project directory
cd "C:\Users\<USER>\projects\fast api sureal"

# 2. Create environment from the provided environment.yml
conda env create -f environment.yml

# 3. Activate the environment
conda activate myenv

# 4. Verify installation
python --version
pip list
```

### Step 3: Set Up Auto-Activation

**Method 1: Use Provided Scripts**

For Command Prompt:
```bash
# Double-click activate_env.bat or run:
activate_env.bat
```

For PowerShell:
```powershell
# Run in PowerShell:
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\activate_env.ps1
```

**Method 2: VS Code Integration**

1. Open VS Code in the project directory
2. Press `Ctrl+Shift+P`
3. Type "Python: Select Interpreter"
4. Choose the interpreter from `C:\Users\<USER>\anaconda3\envs\myenv\python.exe`
5. VS Code will automatically activate this environment for terminals

**Method 3: Windows Terminal Auto-Activation**

Add to your PowerShell profile:
```powershell
# Edit profile
notepad $PROFILE

# Add this line:
if (Test-Path "C:\Users\<USER>\projects\fast api sureal") {
    Set-Location "C:\Users\<USER>\projects\fast api sureal"
    conda activate myenv
}
```

### Step 4: Verify Setup

Test that everything works:

```bash
# 1. Open new terminal/command prompt
# 2. Navigate to project
cd "C:\Users\<USER>\projects\fast api sureal"

# 3. Environment should auto-activate or run:
conda activate myenv

# 4. Test Python and packages
python -c "import fastapi, surrealdb, sklearn; print('✓ All packages working')"

# 5. Test the application
python backend/main.py
```

## Alternative Solutions

### If Conda Continues to Fail

**Option 1: Use Python venv instead**
```bash
# Create virtual environment
python -m venv venv

# Activate (Windows)
venv\Scripts\activate

# Install requirements
pip install -r requirements.txt
```

**Option 2: Use Poetry**
```bash
# Install Poetry
pip install poetry

# Create pyproject.toml and install
poetry init
poetry install
poetry shell
```

## Permanent Auto-Activation Solutions

### Solution 1: Project-Specific Batch File

Create `start_project.bat`:
```batch
@echo off
cd /d "C:\Users\<USER>\projects\fast api sureal"
call conda activate myenv
cmd /k
```

### Solution 2: Windows Shortcut

1. Right-click Desktop > New > Shortcut
2. Target: `cmd /k "cd /d C:\Users\<USER>\projects\fast api sureal && conda activate myenv"`
3. Name: "Psychiatric ML Project"

### Solution 3: VS Code Workspace Settings

Create `.vscode/settings.json`:
```json
{
    "python.defaultInterpreterPath": "C:\\Users\\<USER>\\anaconda3\\envs\\myenv\\python.exe",
    "terminal.integrated.env.windows": {
        "CONDA_DEFAULT_ENV": "myenv"
    },
    "python.terminal.activateEnvironment": true
}
```

## Troubleshooting

### Common Issues

1. **"conda: command not found"**
   - Add `C:\Users\<USER>\anaconda3\Scripts` to PATH
   - Restart terminal

2. **"Environment already exists"**
   - Remove existing: `conda env remove -n myenv`
   - Recreate: `conda env create -f environment.yml`

3. **Permission errors**
   - Run terminal as Administrator
   - Check antivirus software

4. **Package conflicts**
   - Clear conda cache: `conda clean --all`
   - Update conda: `conda update conda`

### Verification Commands

```bash
# Check conda installation
conda info

# List environments
conda env list

# Check current environment
echo $CONDA_DEFAULT_ENV

# Test Python packages
python -c "import sys; print(sys.executable)"
```

## Next Steps

After fixing the conda environment:

1. ✅ Environment auto-activates in new terminals
2. ✅ All required packages are installed
3. ✅ Python interpreter points to correct environment
4. ✅ Ready to debug and run the main application

The next phase will focus on debugging the FastAPI application using the provided debug guides.
