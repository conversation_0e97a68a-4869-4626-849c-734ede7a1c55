import pandas as pd
import numpy as np
from typing import Dict, List, Any, <PERSON><PERSON>, Optional
from sklearn.preprocessing import Label<PERSON>nco<PERSON>, OneHotEncoder, StandardScaler
import joblib
import os

class DemographicFeatureEncoder:
    """Transform demographic data into ML-ready features"""
    
    def __init__(self):
        self.encoders = {}
        self.feature_names = []
        self.is_fitted = False
        # Cache fitted encoders
        self._encoder_cache = {}
    
    def fit_transform(self, demographic_data: List[Dict]) -> np.ndarray:
        """Fit encoders and transform demographic data"""
        df = pd.DataFrame(demographic_data)
        
        # One-hot encoding for nominal categories
        nominal_features = ['gender', 'occupation', 'living_situation', 'referral_source']
        for feature in nominal_features:
            if feature in df.columns:
                # Check if encoder is already cached
                cache_key = f"{feature}_onehot"
                if cache_key in self._encoder_cache:
                    encoder = self._encoder_cache[cache_key]
                else:
                    encoder = OneHotEncoder(sparse_output=False, handle_unknown='ignore')
                    encoder.fit(df[[feature]])
                    self._encoder_cache[cache_key] = encoder
                
                encoded = encoder.transform(df[[feature]])
                
                # Store encoder and feature names
                self.encoders[cache_key] = encoder
                feature_names = [f"{feature}_{cat}" for cat in encoder.categories_[0]]
                self.feature_names.extend(feature_names)
        
        # Ordinal encoding for ordered categories
        ordinal_mappings = {
            'age_group': {'18-25': 1, '26-35': 2, '36-45': 3, '46-55': 4, '56-65': 5, '65+': 6},
            'education': {'Primary': 1, 'Secondary': 2, 'Bachelor': 3, 'Graduate': 4, 'Professional': 5}
        }
        
        for feature, mapping in ordinal_mappings.items():
            if feature in df.columns:
                df[f"{feature}_ordinal"] = df[feature].map(mapping)
                self.feature_names.append(f"{feature}_ordinal")
        
        self.is_fitted = True
        return self._combine_features(df)
    
    def extract_features(self, demographic_data: Dict) -> Dict[str, float]:
        """Transform new demographic data using fitted encoders"""
        features = {}
        
        # Process nominal features with one-hot encoding
        for feature in ['gender', 'occupation', 'living_situation', 'referral_source']:
            if feature in demographic_data:
                cache_key = f"{feature}_onehot"
                encoder = self._encoder_cache.get(cache_key)
                if encoder:
                    value = demographic_data[feature]
                    try:
                        encoded = encoder.transform([[value]])[0]
                        for i, category in enumerate(encoder.categories_[0]):
                            features[f"{feature}_{category}"] = float(encoded[i])
                    except:
                        # Handle unknown categories
                        for category in encoder.categories_[0]:
                            features[f"{feature}_{category}"] = 0.0
        
        # Process ordinal features
        ordinal_mappings = {
            'age_group': {'18-25': 1, '26-35': 2, '36-45': 3, '46-55': 4, '56-65': 5, '65+': 6},
            'education': {'Primary': 1, 'Secondary': 2, 'Bachelor': 3, 'Graduate': 4, 'Professional': 5}
        }
        
        for feature, mapping in ordinal_mappings.items():
            if feature in demographic_data:
                features[f"{feature}_ordinal"] = float(mapping.get(demographic_data[feature], 0))
        
        return features
    
    def _combine_features(self, df: pd.DataFrame) -> np.ndarray:
        """Combine all features into a single matrix"""
        feature_cols = [col for col in df.columns if any(col.startswith(prefix) for prefix in 
                      ['gender_', 'occupation_', 'living_situation_', 'referral_source_', 
                       'age_group_ordinal', 'education_ordinal'])]
        
        return df[feature_cols].values if feature_cols else np.array([])

class SymptomFeatureEncoder:
    """Extract features from psychiatric symptom assessments"""
    
    def __init__(self):
        self.symptom_domains = ['mood', 'psychotic', 'anxiety', 'cognitive', 'behavioral']
        self.severity_mapping = {'Mild': 1, 'Moderate': 2, 'Severe': 3}
    
    def extract_features(self, symptom_data: Dict) -> Dict[str, float]:
        """Extract comprehensive symptom features"""
        features = {}
        
        for domain in self.symptom_domains:
            domain_symptoms = symptom_data.get(f"{domain}_symptoms", {})
            
            # Basic counts
            features[f"{domain}_symptom_count"] = sum(1 for s in domain_symptoms.values() if s.get('present', False))
            
            # Severity metrics
            severities = [self.severity_mapping.get(s.get('severity'), 0) 
                         for s in domain_symptoms.values() if s.get('present', False)]
            
            if severities:
                features[f"{domain}_max_severity"] = max(severities)
                features[f"{domain}_avg_severity"] = np.mean(severities)
                features[f"{domain}_severe_count"] = sum(1 for s in severities if s == 3)
            else:
                features[f"{domain}_max_severity"] = 0
                features[f"{domain}_avg_severity"] = 0
                features[f"{domain}_severe_count"] = 0
            
            # Duration features
            durations = [s.get('duration_weeks', 0) 
                        for s in domain_symptoms.values() if s.get('present', False)]
            
            if durations:
                features[f"{domain}_max_duration"] = max(durations)
                features[f"{domain}_avg_duration"] = np.mean(durations)
                features[f"{domain}_chronic_count"] = sum(1 for d in durations if d > 26)  # >6 months
            else:
                features[f"{domain}_max_duration"] = 0
                features[f"{domain}_avg_duration"] = 0
                features[f"{domain}_chronic_count"] = 0
        
        # Cross-domain features
        total_symptoms = sum(features[f"{domain}_symptom_count"] for domain in self.symptom_domains)
        features['total_symptom_count'] = total_symptoms
        features['symptom_domain_breadth'] = sum(1 for domain in self.symptom_domains 
                                               if features[f"{domain}_symptom_count"] > 0)
        
        # Severity index (weighted composite score)
        severity_weights = {'mood': 0.3, 'psychotic': 0.25, 'anxiety': 0.2, 'cognitive': 0.15, 'behavioral': 0.1}
        weighted_severity = sum(severity_weights[domain] * features[f"{domain}_avg_severity"]
                               for domain in self.symptom_domains)
        features['composite_severity_index'] = weighted_severity

        return features

class LabFeatureEncoder:
    """Process laboratory data for ML features"""

    def __init__(self):
        self.lab_parameters = {
            'CBC': ['WBC', 'RBC', 'Hemoglobin', 'Hematocrit', 'Platelets', 'Neutrophils_%', 'Lymphocytes_%'],
            'Metabolic': ['Glucose', 'BUN', 'Creatinine', 'eGFR', 'Sodium', 'Potassium', 'Chloride'],
            'Liver': ['AST', 'ALT', 'ALP', 'Total_Bilirubin', 'Albumin'],
            'Thyroid': ['TSH', 'Free_T4', 'Free_T3']
        }
        self.reference_ranges = self._load_reference_ranges()

    def extract_features(self, lab_data: List[Dict]) -> Dict[str, float]:
        """Extract ML features from lab results"""
        if not lab_data:
            return self._get_missing_lab_features()

        # Use most recent labs of each type
        latest_labs = {}
        for lab in lab_data:
            lab_type = lab['lab_type']
            if lab_type not in latest_labs or lab['collection_date'] > latest_labs[lab_type]['collection_date']:
                latest_labs[lab_type] = lab

        features = {}

        for lab_type, lab_result in latest_labs.items():
            raw_values = lab_result.get('raw_values', {})

            # Raw value features (normalized)
            for param, value in raw_values.items():
                if value is not None:
                    # Z-score normalization
                    ref_range = self.reference_ranges.get(param)
                    if ref_range:
                        low, high = ref_range
                        mid = (low + high) / 2
                        std = (high - low) / 4  # Assume 2-sigma reference range
                        z_score = (value - mid) / std
                        features[f"{param}_zscore"] = z_score

                        # Abnormality flags
                        if value < low:
                            features[f"{param}_abnormal"] = -1
                        elif value > high:
                            features[f"{param}_abnormal"] = 1
                        else:
                            features[f"{param}_abnormal"] = 0

            # Derived ratio features (clinically significant)
            if lab_type == 'Liver' and 'AST' in raw_values and 'ALT' in raw_values:
                if raw_values['AST'] and raw_values['ALT']:
                    features['AST_ALT_ratio'] = raw_values['AST'] / raw_values['ALT']

            if lab_type == 'Metabolic' and 'BUN' in raw_values and 'Creatinine' in raw_values:
                if raw_values['BUN'] and raw_values['Creatinine']:
                    features['BUN_Creatinine_ratio'] = raw_values['BUN'] / raw_values['Creatinine']

            if lab_type == 'CBC' and 'Neutrophils_%' in raw_values and 'Lymphocytes_%' in raw_values:
                if raw_values['Neutrophils_%'] and raw_values['Lymphocytes_%']:
                    features['Neutrophil_Lymphocyte_ratio'] = raw_values['Neutrophils_%'] / raw_values['Lymphocytes_%']

        # Aggregate abnormality metrics
        abnormal_counts = [v for k, v in features.items() if k.endswith('_abnormal') and v != 0]
        features['total_abnormal_labs'] = len(abnormal_counts)
        features['abnormal_lab_severity'] = sum(abs(v) for v in abnormal_counts)

        return features

    def _load_reference_ranges(self) -> Dict[str, Tuple[float, float]]:
        """Load standard reference ranges for lab parameters"""
        return {
            'WBC': (4.0, 11.0),
            'RBC': (4.2, 5.4),
            'Hemoglobin': (12.0, 16.0),
            'Hematocrit': (36.0, 48.0),
            'Platelets': (150, 450),
            'Glucose': (70, 100),
            'BUN': (7, 20),
            'Creatinine': (0.6, 1.2),
            'AST': (10, 40),
            'ALT': (7, 35),
            'TSH': (0.4, 4.0),
            # Add more reference ranges as needed
        }

    def _get_missing_lab_features(self) -> Dict[str, float]:
        """Return default features when no lab data is available"""
        return {
            'total_abnormal_labs': 0,
            'abnormal_lab_severity': 0,
            'lab_data_available': 0
        }

class HistoryFeatureEncoder:
    """Extract features from clinical history"""

    def extract_features(self, history_data: Dict) -> Dict[str, float]:
        """Extract ML features from clinical history"""
        features = {}

        # Previous psychiatric history
        features['previous_diagnoses_count'] = len(history_data.get('previous_diagnoses', []))
        features['hospitalization_count'] = float(history_data.get('hospitalization_count', 0))
        features['suicide_attempts'] = float(history_data.get('suicide_attempts', 0))
        features['medication_trials_count'] = len(history_data.get('medication_trials', {}))

        # Family history
        features['family_psychiatric_history'] = float(history_data.get('family_psychiatric_history', False))
        features['family_conditions_count'] = len(history_data.get('family_conditions', []))

        # Substance use
        substance_use = history_data.get('substance_use', {})
        features['active_substance_use'] = float(any(
            item.get('current_use', False) for item in substance_use.values()
        ))
        features['substance_types_count'] = len([
            k for k, v in substance_use.items() if v.get('current_use', False)
        ])

        # Social factors
        social_support_map = {'High': 4, 'Moderate': 3, 'Low': 2, 'Minimal': 1}
        features['social_support_level'] = social_support_map.get(
            history_data.get('social_support_level', 'Moderate'), 3
        )

        housing_stability_map = {'Stable': 4, 'Temporary': 3, 'Unstable': 2, 'Homeless': 1}
        features['housing_stability'] = housing_stability_map.get(
            history_data.get('housing_stability', 'Stable'), 4
        )

        features['trauma_history'] = float(history_data.get('trauma_history', False))

        return features
