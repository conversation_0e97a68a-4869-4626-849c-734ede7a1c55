from fastapi import APIRouter, HTTPException, Depends, status, BackgroundTasks
from pydantic import BaseModel
from typing import Dict, List, Any, Optional
from datetime import datetime
from database.connection import SurrealDBManager
from security.auth_manager import get_current_active_user
from security.pid_manager import Secure<PERSON><PERSON><PERSON>andler
from ml.pipeline import PsychiatricML<PERSON>ipeline
from utils.logging import logger
from dependencies import get_db, get_ml_pipeline, get_pid_handler
import asyncio

router = APIRouter(prefix="/ml", tags=["machine_learning"])

# Global ML pipeline instance
ml_pipeline = PsychiatricMLPipeline()

# Dependency to get PID handler
def get_pid_handler():
    return SecurePIDHandler()

class PredictionRequest(BaseModel):
    pid: str

class PredictionResponse(BaseModel):
    pid: str
    predictions: Dict[str, Any]
    timestamp: datetime
    data_completeness: float

class TrainingRequest(BaseModel):
    force_retrain: bool = False

class TrainingResponse(BaseModel):
    status: str
    message: str
    performance_metrics: Optional[Dict[str, float]] = None
    training_samples: Optional[int] = None

class FeedbackRequest(BaseModel):
    prediction_id: str
    feedback: str
    correct_diagnosis: Optional[str] = None

@router.post("/predict", response_model=PredictionResponse)
async def predict_patient_outcomes(
    request: PredictionRequest,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    db: SurrealDBManager = Depends(get_db),
    pid_handler: SecurePIDHandler = Depends(get_pid_handler)
):
    """Generate ML predictions for a patient"""
    
    # Validate PID
    if not pid_handler.validate_pid_integrity(request.pid):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid PID format"
        )
    
    # Get patient data
    patient = await db.get_patient(request.pid)
    if not patient:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Patient not found"
        )
    
    # Check permissions
    if patient.get("clinician_id") != current_user["user_id"] and "admin" not in current_user.get("roles", []):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this patient record"
        )
    
    try:
        # Prepare patient data for ML pipeline
        patient_data = {
            "demographics": patient.get("demographics", {}),
            "symptoms": patient.get("symptoms", {}),
            "clinical_history": patient.get("clinical_history", {}),
            "recent_labs": []  # Would be populated from lab_results table
        }
        
        # Generate predictions
        predictions = await ml_pipeline.predict(patient_data)
        
        # Store prediction in database
        prediction_record = {
            "patient": f"patients:{patient['pid']}",
            "prediction_type": "comprehensive",
            "prediction_value": predictions.get("diagnosis", {}).get("predicted_class", "Unknown"),
            "confidence_score": predictions.get("diagnosis", {}).get("confidence", 0.0),
            "probability_distribution": predictions,
            "prediction_timestamp": datetime.now(),
            "clinician_feedback": None
        }
        
        prediction_id = await db.store_ml_prediction(prediction_record)
        
        logger.info(f"ML prediction generated for patient {request.pid} by {current_user['username']}")
        
        return PredictionResponse(
            pid=request.pid,
            predictions=predictions,
            timestamp=datetime.now(),
            data_completeness=predictions.get("metadata", {}).get("data_completeness", 0.0)
        )
        
    except Exception as e:
        logger.error(f"Failed to generate predictions for patient {request.pid}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate predictions: {str(e)}"
        )

@router.post("/train", response_model=TrainingResponse)
async def train_models(
    request: TrainingRequest,
    background_tasks: BackgroundTasks,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    db: SurrealDBManager = Depends(get_db)
):
    """Train or retrain ML models"""
    
    # Check admin permissions
    if "admin" not in current_user.get("roles", []):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin role required for model training"
        )
    
    try:
        # Check if models exist and performance
        if not request.force_retrain and ml_pipeline.is_trained:
            current_performance = ml_pipeline.model_performance
            if current_performance.get("diagnosis_accuracy", 0) > 0.8:
                return TrainingResponse(
                    status="skipped",
                    message="Models already trained with good performance. Use force_retrain=true to retrain.",
                    performance_metrics=current_performance
                )
        
        # Get training data
        training_data = await db.get_ml_training_data()
        
        if len(training_data) < 50:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Insufficient training data: {len(training_data)} samples (minimum 50 required)"
            )
        
        # Start training in background
        background_tasks.add_task(
            _train_models_background,
            training_data,
            current_user["username"]
        )
        
        logger.info(f"Model training initiated by {current_user['username']} with {len(training_data)} samples")
        
        return TrainingResponse(
            status="started",
            message="Model training started in background",
            training_samples=len(training_data)
        )
        
    except Exception as e:
        logger.error(f"Failed to start model training: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start training: {str(e)}"
        )

async def _train_models_background(training_data: List[Dict], username: str):
    """Background task for model training"""
    try:
        logger.info(f"Starting background model training with {len(training_data)} samples")
        
        # Prepare training data
        X, labels = await ml_pipeline.prepare_training_data_from_records(training_data)
        
        # Train models
        performance = await ml_pipeline.train_models(X, labels)
        
        logger.info(f"Model training completed successfully by {username}. Performance: {performance}")
        
    except Exception as e:
        logger.error(f"Background model training failed: {e}")

@router.get("/status")
async def get_model_status(
    current_user: Dict[str, Any] = Depends(get_current_active_user)
):
    """Get current model training status and performance"""
    
    try:
        # Try to load models if not already loaded
        if not ml_pipeline.is_trained:
            try:
                await ml_pipeline.load_models()
            except:
                pass  # Models might not exist yet
        
        status_info = {
            "is_trained": ml_pipeline.is_trained,
            "model_performance": ml_pipeline.model_performance,
            "training_metadata": ml_pipeline.training_metadata,
            "feature_count": len(ml_pipeline.feature_names) if ml_pipeline.feature_names else 0
        }
        
        return status_info
        
    except Exception as e:
        logger.error(f"Failed to get model status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get model status: {str(e)}"
        )

@router.post("/feedback")
async def submit_prediction_feedback(
    request: FeedbackRequest,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    db: SurrealDBManager = Depends(get_db)
):
    """Submit feedback on ML predictions for model improvement"""
    
    try:
        # Update prediction record with feedback
        # This would require implementing an update method in the database manager
        # For now, just log the feedback
        
        logger.info(
            f"Prediction feedback received from {current_user['username']}: "
            f"Prediction ID: {request.prediction_id}, Feedback: {request.feedback}"
        )
        
        return {"message": "Feedback submitted successfully"}
        
    except Exception as e:
        logger.error(f"Failed to submit feedback: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to submit feedback: {str(e)}"
        )
