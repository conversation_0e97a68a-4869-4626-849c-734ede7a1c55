import asyncio
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import cross_val_score, GridSearchCV, train_test_split
from sklearn.metrics import classification_report, mean_squared_error, roc_auc_score, accuracy_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
import joblib
import json
from pathlib import Path
from datetime import datetime
from ml.features import DemographicFeatureEncoder, SymptomFeatureEncoder, LabFeatureEncoder, HistoryFeatureEncoder
from utils.logging import logger

class PsychiatricMLPipeline:
    """Complete ML pipeline for psychiatric predictions"""
    
    def __init__(self, models_path: str = "./models"):
        self.models_path = Path(models_path)
        self.models_path.mkdir(exist_ok=True)
        
        # Feature encoders
        self.feature_encoders = {
            'demographics': DemographicFeatureEncoder(),
            'symptoms': SymptomFeatureEncoder(),
            'labs': LabFeatureEncoder(),
            'history': HistoryFeatureEncoder()
        }
        
        # ML models
        self.models = {
            'diagnosis_predictor': RandomForestClassifier(
                n_estimators=200,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                class_weight='balanced'
            ),
            'severity_predictor': RandomForestRegressor(
                n_estimators=200,
                max_depth=12,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42
            ),
            'risk_stratifier': LogisticRegression(
                random_state=42,
                class_weight='balanced',
                max_iter=1000
            )
        }
        
        # Model metadata
        self.feature_names = []
        self.label_encoders = {}
        self.scalers = {}
        self.is_trained = False
        self.model_performance = {}
        self.training_metadata = {}
    
    async def prepare_training_data(self, db_manager) -> Tuple[np.ndarray, Dict[str, np.ndarray]]:
        """Extract and prepare training data from database"""
        logger.info("Extracting training data from database...")
        
        # Get complete patient records
        training_data = await db_manager.get_ml_training_data()
        
        if len(training_data) < 10:
            raise ValueError(f"Insufficient training data: {len(training_data)} samples (minimum 10 required)")
        
        logger.info(f"Processing {len(training_data)} patient records...")
        
        # Extract features
        X_features = []
        y_diagnosis = []
        y_severity = []
        y_risk = []
        
        for record in training_data:
            try:
                # Extract features from each data source
                features = {}
                
                # Demographics features
                if record.get('demographics'):
                    demo_features = self.feature_encoders['demographics'].extract_features(record['demographics'])
                    features.update(demo_features)
                
                # Symptom features
                if record.get('symptoms'):
                    symptom_features = self.feature_encoders['symptoms'].extract_features(record['symptoms'])
                    features.update(symptom_features)
                
                # Lab features
                if record.get('recent_labs'):
                    lab_features = self.feature_encoders['labs'].extract_features(record['recent_labs'])
                    features.update(lab_features)
                
                # Clinical history features
                if record.get('clinical_history'):
                    history_features = self.feature_encoders['history'].extract_features(record['clinical_history'])
                    features.update(history_features)
                
                # Derived features
                derived_features = self._compute_derived_features(features)
                features.update(derived_features)
                
                X_features.append(features)
                
                # Extract labels (this would need to be adapted based on your actual data structure)
                # For demonstration, creating synthetic labels based on symptom severity
                y_diagnosis.append(self._infer_diagnosis_label(record))
                y_severity.append(self._compute_severity_score(record))
                y_risk.append(self._compute_risk_level(record))
                
            except Exception as e:
                logger.warning(f"Skipping record due to error: {e}")
                continue
        
        # Convert to consistent feature matrix
        feature_df = pd.DataFrame(X_features).fillna(0)
        self.feature_names = feature_df.columns.tolist()
        
        X = feature_df.values
        labels = {
            'diagnosis': np.array(y_diagnosis),
            'severity': np.array(y_severity),
            'risk': np.array(y_risk)
        }
        
        logger.info(f"Training data prepared: {X.shape[0]} samples, {X.shape[1]} features")
        return X, labels

    async def train_models(self, X: np.ndarray, labels: Dict[str, np.ndarray]) -> Dict[str, float]:
        """Train all ML models with cross-validation"""
        logger.info("Starting model training...")

        performance_metrics = {}

        # Scale features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        self.scalers['feature_scaler'] = scaler

        # Train diagnosis predictor
        logger.info("Training diagnosis predictor...")
        y_diagnosis = labels['diagnosis']

        # Encode diagnosis labels
        diagnosis_encoder = LabelEncoder()
        y_diagnosis_encoded = diagnosis_encoder.fit_transform(y_diagnosis)
        self.label_encoders['diagnosis'] = diagnosis_encoder

        # Cross-validation for diagnosis model
        cv_scores = cross_val_score(
            self.models['diagnosis_predictor'],
            X_scaled,
            y_diagnosis_encoded,
            cv=5,
            scoring='accuracy'
        )

        # Train final diagnosis model
        self.models['diagnosis_predictor'].fit(X_scaled, y_diagnosis_encoded)
        performance_metrics['diagnosis_accuracy'] = cv_scores.mean()
        performance_metrics['diagnosis_std'] = cv_scores.std()

        logger.info(f"Diagnosis model accuracy: {cv_scores.mean():.3f} ± {cv_scores.std():.3f}")

        # Train severity predictor
        logger.info("Training severity predictor...")
        y_severity = labels['severity']

        cv_scores = cross_val_score(
            self.models['severity_predictor'],
            X_scaled,
            y_severity,
            cv=5,
            scoring='neg_mean_squared_error'
        )

        self.models['severity_predictor'].fit(X_scaled, y_severity)
        performance_metrics['severity_mse'] = -cv_scores.mean()
        performance_metrics['severity_std'] = cv_scores.std()

        logger.info(f"Severity model MSE: {-cv_scores.mean():.3f} ± {cv_scores.std():.3f}")

        # Train risk stratifier
        logger.info("Training risk stratifier...")
        y_risk = labels['risk']

        # Encode risk labels
        risk_encoder = LabelEncoder()
        y_risk_encoded = risk_encoder.fit_transform(y_risk)
        self.label_encoders['risk'] = risk_encoder

        cv_scores = cross_val_score(
            self.models['risk_stratifier'],
            X_scaled,
            y_risk_encoded,
            cv=5,
            scoring='roc_auc'
        )

        self.models['risk_stratifier'].fit(X_scaled, y_risk_encoded)
        performance_metrics['risk_auc'] = cv_scores.mean()
        performance_metrics['risk_std'] = cv_scores.std()

        logger.info(f"Risk model AUC: {cv_scores.mean():.3f} ± {cv_scores.std():.3f}")

        self.is_trained = True
        self.model_performance = performance_metrics
        self.training_metadata = {
            'training_date': datetime.now().isoformat(),
            'n_samples': X.shape[0],
            'n_features': X.shape[1],
            'feature_names': self.feature_names
        }

        # Save models
        await self.save_models()

        logger.info("Model training completed successfully")
        return performance_metrics

    async def predict(self, patient_data: Dict[str, Any]) -> Dict[str, Any]:
        """Make predictions for a single patient"""
        if not self.is_trained:
            await self.load_models()

        # Extract features
        features = {}

        # Demographics features
        if patient_data.get('demographics'):
            demo_features = self.feature_encoders['demographics'].extract_features(patient_data['demographics'])
            features.update(demo_features)

        # Symptom features
        if patient_data.get('symptoms'):
            symptom_features = self.feature_encoders['symptoms'].extract_features(patient_data['symptoms'])
            features.update(symptom_features)

        # Lab features
        if patient_data.get('recent_labs'):
            lab_features = self.feature_encoders['labs'].extract_features(patient_data['recent_labs'])
            features.update(lab_features)

        # Clinical history features
        if patient_data.get('clinical_history'):
            history_features = self.feature_encoders['history'].extract_features(patient_data['clinical_history'])
            features.update(history_features)

        # Derived features
        derived_features = self._compute_derived_features(features)
        features.update(derived_features)

        # Convert to feature vector
        feature_vector = np.array([features.get(name, 0) for name in self.feature_names]).reshape(1, -1)

        # Scale features
        if 'feature_scaler' in self.scalers:
            feature_vector = self.scalers['feature_scaler'].transform(feature_vector)

        # Make predictions
        predictions = {}

        # Diagnosis prediction
        if 'diagnosis_predictor' in self.models:
            diagnosis_proba = self.models['diagnosis_predictor'].predict_proba(feature_vector)[0]
            diagnosis_pred = self.models['diagnosis_predictor'].predict(feature_vector)[0]

            # Decode diagnosis
            diagnosis_label = self.label_encoders['diagnosis'].inverse_transform([diagnosis_pred])[0]

            predictions['diagnosis'] = {
                'predicted_class': diagnosis_label,
                'confidence': float(max(diagnosis_proba)),
                'probability_distribution': {
                    label: float(prob) for label, prob in
                    zip(self.label_encoders['diagnosis'].classes_, diagnosis_proba)
                }
            }

        # Severity prediction
        if 'severity_predictor' in self.models:
            severity_pred = self.models['severity_predictor'].predict(feature_vector)[0]
            predictions['severity'] = {
                'predicted_score': float(severity_pred),
                'severity_level': self._categorize_severity(severity_pred)
            }

        # Risk prediction
        if 'risk_stratifier' in self.models:
            risk_proba = self.models['risk_stratifier'].predict_proba(feature_vector)[0]
            risk_pred = self.models['risk_stratifier'].predict(feature_vector)[0]

            # Decode risk
            risk_label = self.label_encoders['risk'].inverse_transform([risk_pred])[0]

            predictions['risk'] = {
                'predicted_class': risk_label,
                'confidence': float(max(risk_proba)),
                'probability_distribution': {
                    label: float(prob) for label, prob in
                    zip(self.label_encoders['risk'].classes_, risk_proba)
                }
            }

        # Add metadata
        predictions['metadata'] = {
            'prediction_timestamp': datetime.now().isoformat(),
            'model_version': self.training_metadata.get('training_date', 'unknown'),
            'feature_count': len(self.feature_names),
            'data_completeness': self._compute_data_completeness(features)
        }

        return predictions

    def _compute_derived_features(self, features: Dict[str, float]) -> Dict[str, float]:
        """Compute derived features from base features"""
        derived = {}

        # Symptom interaction features
        if 'mood_symptom_count' in features and 'anxiety_symptom_count' in features:
            derived['mood_anxiety_interaction'] = features['mood_symptom_count'] * features['anxiety_symptom_count']

        # Risk factor combinations
        if 'suicide_attempts' in features and 'substance_types_count' in features:
            derived['suicide_substance_risk'] = features['suicide_attempts'] * features['substance_types_count']

        # Chronicity indicators
        if 'hospitalization_count' in features and 'previous_diagnoses_count' in features:
            derived['chronicity_index'] = features['hospitalization_count'] + features['previous_diagnoses_count']

        return derived

    def _infer_diagnosis_label(self, record: Dict[str, Any]) -> str:
        """Infer diagnosis label from patient record (simplified for demo)"""
        symptoms = record.get('symptoms', {})

        # Simple rule-based inference for demonstration
        mood_symptoms = symptoms.get('mood_symptoms', {})
        psychotic_symptoms = symptoms.get('psychotic_symptoms', {})
        anxiety_symptoms = symptoms.get('anxiety_symptoms', {})

        mood_count = sum(1 for s in mood_symptoms.values() if s.get('present', False))
        psychotic_count = sum(1 for s in psychotic_symptoms.values() if s.get('present', False))
        anxiety_count = sum(1 for s in anxiety_symptoms.values() if s.get('present', False))

        if psychotic_count > 2:
            return "Psychotic_Disorder"
        elif mood_count > 3:
            return "Mood_Disorder"
        elif anxiety_count > 3:
            return "Anxiety_Disorder"
        else:
            return "Other"

    def _compute_severity_score(self, record: Dict[str, Any]) -> float:
        """Compute overall severity score (0-10 scale)"""
        symptoms = record.get('symptoms', {})

        severity_sum = 0
        severity_count = 0

        severity_map = {'Mild': 1, 'Moderate': 2, 'Severe': 3}

        for domain_symptoms in symptoms.values():
            if isinstance(domain_symptoms, dict):
                for symptom in domain_symptoms.values():
                    if symptom.get('present', False) and symptom.get('severity'):
                        severity_sum += severity_map.get(symptom['severity'], 0)
                        severity_count += 1

        if severity_count == 0:
            return 0.0

        # Normalize to 0-10 scale
        avg_severity = severity_sum / severity_count
        return min(10.0, avg_severity * 3.33)  # Scale 1-3 to 0-10

    def _compute_risk_level(self, record: Dict[str, Any]) -> str:
        """Compute risk level based on clinical factors"""
        history = record.get('clinical_history', {})

        risk_score = 0

        # Risk factors
        if history.get('suicide_attempts', 0) > 0:
            risk_score += 3
        if history.get('hospitalization_count', 0) > 2:
            risk_score += 2
        if history.get('substance_use', {}):
            active_use = any(item.get('current_use', False) for item in history['substance_use'].values())
            if active_use:
                risk_score += 2
        if history.get('trauma_history', False):
            risk_score += 1

        if risk_score >= 5:
            return "High"
        elif risk_score >= 3:
            return "Moderate"
        else:
            return "Low"

    def _categorize_severity(self, severity_score: float) -> str:
        """Categorize continuous severity score"""
        if severity_score >= 7.0:
            return "Severe"
        elif severity_score >= 4.0:
            return "Moderate"
        elif severity_score >= 1.0:
            return "Mild"
        else:
            return "Minimal"

    def _compute_data_completeness(self, features: Dict[str, float]) -> float:
        """Compute data completeness score"""
        total_features = len(self.feature_names)
        present_features = sum(1 for name in self.feature_names if features.get(name, 0) != 0)
        return present_features / total_features if total_features > 0 else 0.0

    async def save_models(self):
        """Save trained models to disk"""
        try:
            # Save models
            for name, model in self.models.items():
                model_path = self.models_path / f"{name}.joblib"
                joblib.dump(model, model_path)

            # Save encoders and scalers
            for name, encoder in self.label_encoders.items():
                encoder_path = self.models_path / f"{name}_encoder.joblib"
                joblib.dump(encoder, encoder_path)

            for name, scaler in self.scalers.items():
                scaler_path = self.models_path / f"{name}.joblib"
                joblib.dump(scaler, scaler_path)

            # Save metadata
            metadata = {
                'feature_names': self.feature_names,
                'model_performance': self.model_performance,
                'training_metadata': self.training_metadata
            }

            metadata_path = self.models_path / "metadata.json"
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2)

            logger.info(f"Models saved to {self.models_path}")

        except Exception as e:
            logger.error(f"Failed to save models: {e}")
            raise

    async def load_models(self):
        """Load trained models from disk"""
        try:
            # Load metadata
            metadata_path = self.models_path / "metadata.json"
            if metadata_path.exists():
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)

                self.feature_names = metadata.get('feature_names', [])
                self.model_performance = metadata.get('model_performance', {})
                self.training_metadata = metadata.get('training_metadata', {})

            # Load models
            for name in self.models.keys():
                model_path = self.models_path / f"{name}.joblib"
                if model_path.exists():
                    self.models[name] = joblib.load(model_path)

            # Load encoders
            for name in ['diagnosis', 'risk']:
                encoder_path = self.models_path / f"{name}_encoder.joblib"
                if encoder_path.exists():
                    self.label_encoders[name] = joblib.load(encoder_path)

            # Load scalers
            scaler_path = self.models_path / "feature_scaler.joblib"
            if scaler_path.exists():
                self.scalers['feature_scaler'] = joblib.load(scaler_path)

            self.is_trained = True
            logger.info("Models loaded successfully")

        except Exception as e:
            logger.error(f"Failed to load models: {e}")
            raise

    async def prepare_training_data_from_records(self, training_data: List[Dict]) -> Tuple[np.ndarray, Dict[str, np.ndarray]]:
        """Prepare training data from database records"""
        return await self.prepare_training_data(None, training_data)
