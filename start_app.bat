@echo off
echo ========================================
echo Psychiatric ML FastAPI Application
echo ========================================
echo.

REM Change to project directory
cd /d "C:\Users\<USER>\projects\fast api sureal"

REM Check if virtual environment exists
if not exist "venv\Scripts\python.exe" (
    echo ERROR: Virtual environment not found!
    echo Please run setup_environment.bat first.
    pause
    exit /b 1
)

echo Activating Python virtual environment...
call venv\Scripts\activate.bat

echo.
echo Checking Python and packages...
venv\Scripts\python.exe --version
echo.

echo Starting FastAPI server...
echo Server will be available at: http://localhost:8000
echo API Documentation: http://localhost:8000/docs
echo Health Check: http://localhost:8000/health
echo.
echo Press Ctrl+C to stop the server
echo ========================================
echo.

REM Start the FastAPI application using uvicorn directly
venv\Scripts\python.exe -m uvicorn backend.main:app --host 0.0.0.0 --port 8000 --reload

echo.
echo Server stopped.
pause
